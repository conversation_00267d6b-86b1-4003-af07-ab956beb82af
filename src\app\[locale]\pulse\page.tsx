// src/app/[locale]/pulse/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import PulseSection from '@/components/sections/pulse/PulseSection';

export default async function PulsePage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const translations = {
    title: t('pulse.title'),
    subtitle: t('pulse.subtitle'),
    timeHorizon: {
      title: t('pulse.timeHorizon.title'),
      options: {
        sixMonths: {
          label: t('pulse.timeHorizon.options.sixMonths.label'),
          description: t('pulse.timeHorizon.options.sixMonths.description')
        },
        oneYear: {
          label: t('pulse.timeHorizon.options.oneYear.label'),
          description: t('pulse.timeHorizon.options.oneYear.description')
        },
        fiveYears: {
          label: t('pulse.timeHorizon.options.fiveYears.label'),
          description: t('pulse.timeHorizon.options.fiveYears.description')
        }
      }
    },
    maturityLevel: {
      title: t('pulse.maturityLevel.title'),
      emerging: t('pulse.maturityLevel.emerging'),
      growing: t('pulse.maturityLevel.growing'),
      maturing: t('pulse.maturityLevel.maturing')
    },
    businessImpact: {
      title: t('pulse.businessImpact.title'),
      relevanceScore: t('pulse.businessImpact.relevanceScore'),
      highImpactFor: t('pulse.businessImpact.highImpactFor')
    },
    industries: {
      lifeInsurance: t('pulse.industries.lifeInsurance'),
      techServices: t('pulse.industries.techServices'),
      manufacturing: t('pulse.industries.manufacturing')
    },
    impactLevels: {
      high: t('pulse.impactLevels.high'),
      veryHigh: t('pulse.impactLevels.veryHigh'),
      medium: t('pulse.impactLevels.medium'),
      low: t('pulse.impactLevels.low')
    },
    contentHeader: {
      title: t('pulse.contentHeader.title'),
      matchingFilters: t('pulse.contentHeader.matchingFilters')
    },
    labels: {
      growth: t('pulse.labels.growth'),
      maturity: t('pulse.labels.maturity'),
      relevanceScore: t('pulse.labels.relevanceScore'),
      explore: t('pulse.labels.explore'),
      businessApplications: t('pulse.labels.businessApplications'),
      keyPlayers: t('pulse.labels.keyPlayers')
    },
    noResults: {
      message: t('pulse.noResults.message'),
      resetButton: t('pulse.noResults.resetButton')
    },
    reportCta: {
      title: t('pulse.reportCta.title'),
      description: t('pulse.reportCta.description'),
      button: t('pulse.reportCta.button')
    },
    technologies: {
      // Life Insurance Focused Technologies
      digitalHealthUnderwriting: {
        name: t('pulse.technologies.digitalHealthUnderwriting.name'),
        description: t('pulse.technologies.digitalHealthUnderwriting.description'),
        maturity: 'emerging' as const,
        category: t('pulse.technologies.digitalHealthUnderwriting.category'),
        growthRate: t('pulse.technologies.digitalHealthUnderwriting.growthRate') || '78%',
        relevanceScore: 95,
        industryImpact: {
          lifeInsurance: 'Very High',
          techServices: 'Medium',
          manufacturing: 'Low'
        },
        businessApplications: t('pulse.technologies.digitalHealthUnderwriting.businessApplications', { returnObjects: true }) as string[] || ['Risk assessment automation', 'Real-time health monitoring', 'Personalized premium pricing'],
        predictedGrowth: t('pulse.technologies.digitalHealthUnderwriting.predictedGrowth') || '89% by 2025',
        keyPlayers: t('pulse.technologies.digitalHealthUnderwriting.keyPlayers', { returnObjects: true }) as string[] || ['John Hancock', 'Vitality', 'Oscar Health'],
        timeHorizonData: t('pulse.technologies.digitalHealthUnderwriting.timeHorizonData', { returnObjects: true }) as any
      },
      parametricLifeProducts: {
        name: t('pulse.technologies.parametricLifeProducts.name'),
        description: t('pulse.technologies.parametricLifeProducts.description'),
        maturity: 'growing' as const,
        category: t('pulse.technologies.parametricLifeProducts.category'),
        growthRate: t('pulse.technologies.parametricLifeProducts.growthRate') || '89%',
        relevanceScore: 88,
        industryImpact: {
          lifeInsurance: 'High',
          techServices: 'Medium',
          manufacturing: 'Low'
        },
        businessApplications: t('pulse.technologies.parametricLifeProducts.businessApplications', { returnObjects: true }) as string[] || ['Instant claim processing', 'Reduced fraud', 'Lower operational costs'],
        predictedGrowth: t('pulse.technologies.parametricLifeProducts.predictedGrowth') || '156% by 2025',
        keyPlayers: t('pulse.technologies.parametricLifeProducts.keyPlayers', { returnObjects: true }) as string[] || ['Etherisc', 'Nexus Mutual', 'InsurAce'],
        timeHorizonData: t('pulse.technologies.parametricLifeProducts.timeHorizonData', { returnObjects: true }) as any
      },
      federatedLearning: {
        name: t('pulse.technologies.federatedLearning.name'),
        description: t('pulse.technologies.federatedLearning.description'),
        maturity: 'emerging' as const,
        category: t('pulse.technologies.federatedLearning.category'),
        growthRate: t('pulse.technologies.federatedLearning.growthRate') || '92%',
        relevanceScore: 82,
        industryImpact: {
          lifeInsurance: 'High',
          techServices: 'High',
          manufacturing: 'Medium'
        },
        businessApplications: t('pulse.technologies.federatedLearning.businessApplications', { returnObjects: true }) as string[] || ['Cross-industry risk models', 'Privacy-compliant data sharing', 'Enhanced fraud detection'],
        predictedGrowth: t('pulse.technologies.federatedLearning.predictedGrowth') || '134% by 2025',
        keyPlayers: t('pulse.technologies.federatedLearning.keyPlayers', { returnObjects: true }) as string[] || ['Google', 'IBM', 'NVIDIA'],
        timeHorizonData: t('pulse.technologies.federatedLearning.timeHorizonData', { returnObjects: true }) as any
      },
      causalAiSystems: {
        name: t('pulse.technologies.causalAiSystems.name'),
        description: t('pulse.technologies.causalAiSystems.description'),
        maturity: 'emerging' as const,
        category: t('pulse.technologies.causalAiSystems.category'),
        growthRate: t('pulse.technologies.causalAiSystems.growthRate') || '67%',
        relevanceScore: 79,
        industryImpact: {
          lifeInsurance: 'High',
          techServices: 'Medium',
          manufacturing: 'Medium'
        },
        businessApplications: t('pulse.technologies.causalAiSystems.businessApplications', { returnObjects: true }) as string[] || ['Mortality prediction', 'Policy optimization', 'Risk factor analysis'],
        predictedGrowth: t('pulse.technologies.causalAiSystems.predictedGrowth') || '98% by 2025',
        keyPlayers: t('pulse.technologies.causalAiSystems.keyPlayers', { returnObjects: true }) as string[] || ['Causality', 'Microsoft', 'Amazon'],
        timeHorizonData: t('pulse.technologies.causalAiSystems.timeHorizonData', { returnObjects: true }) as any
      },
      neuralProcessAutomation: {
        name: t('pulse.technologies.neuralProcessAutomation.name'),
        description: t('pulse.technologies.neuralProcessAutomation.description'),
        maturity: 'growing' as const,
        category: t('pulse.technologies.neuralProcessAutomation.category'),
        growthRate: t('pulse.technologies.neuralProcessAutomation.growthRate') || '73%',
        relevanceScore: 85,
        industryImpact: {
          lifeInsurance: 'Very High',
          techServices: 'High',
          manufacturing: 'High'
        },
        businessApplications: t('pulse.technologies.neuralProcessAutomation.businessApplications', { returnObjects: true }) as string[] || ['Claims processing', 'Customer service', 'Policy administration'],
        predictedGrowth: t('pulse.technologies.neuralProcessAutomation.predictedGrowth') || '112% by 2025',
        keyPlayers: t('pulse.technologies.neuralProcessAutomation.keyPlayers', { returnObjects: true }) as string[] || ['UiPath', 'Automation Anywhere', 'Blue Prism'],
        timeHorizonData: t('pulse.technologies.neuralProcessAutomation.timeHorizonData', { returnObjects: true }) as any
      },
      // IoT & Manufacturing Technologies
      digitalTwinEcosystems: {
        name: t('pulse.technologies.digitalTwinEcosystems.name'),
        description: t('pulse.technologies.digitalTwinEcosystems.description'),
        maturity: 'maturing' as const,
        category: t('pulse.technologies.digitalTwinEcosystems.category'),
        growthRate: t('pulse.technologies.digitalTwinEcosystems.growthRate') || '58%',
        relevanceScore: 71,
        industryImpact: {
          lifeInsurance: 'Medium',
          techServices: 'High',
          manufacturing: 'Very High'
        },
        businessApplications: t('pulse.technologies.digitalTwinEcosystems.businessApplications', { returnObjects: true }) as string[] || ['Customer behavior modeling', 'Product simulation', 'Risk scenario testing'],
        predictedGrowth: t('pulse.technologies.digitalTwinEcosystems.predictedGrowth') || '87% by 2025',
        keyPlayers: t('pulse.technologies.digitalTwinEcosystems.keyPlayers', { returnObjects: true }) as string[] || ['Siemens', 'GE Digital', 'Microsoft'],
        timeHorizonData: t('pulse.technologies.digitalTwinEcosystems.timeHorizonData', { returnObjects: true }) as any
      },
      quantumSafeCryptography: {
        name: t('pulse.technologies.quantumSafeCryptography.name'),
        description: t('pulse.technologies.quantumSafeCryptography.description'),
        maturity: 'maturing' as const,
        category: t('pulse.technologies.quantumSafeCryptography.category'),
        growthRate: t('pulse.technologies.quantumSafeCryptography.growthRate') || '45%',
        relevanceScore: 75,
        industryImpact: {
          lifeInsurance: 'High',
          techServices: 'Very High',
          manufacturing: 'Medium'
        },
        businessApplications: t('pulse.technologies.quantumSafeCryptography.businessApplications', { returnObjects: true }) as string[] || ['Data protection', 'Secure communications', 'Future-proof encryption'],
        predictedGrowth: t('pulse.technologies.quantumSafeCryptography.predictedGrowth') || '67% by 2025',
        keyPlayers: t('pulse.technologies.quantumSafeCryptography.keyPlayers', { returnObjects: true }) as string[] || ['IBM', 'Google', 'NIST'],
        timeHorizonData: t('pulse.technologies.quantumSafeCryptography.timeHorizonData', { returnObjects: true }) as any
      }
    }
  };

  return <PulseSection translations={translations} />;
}
