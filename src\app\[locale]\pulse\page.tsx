// src/app/[locale]/pulse/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import PulseSection from '@/components/sections/pulse/PulseSection';

export default async function PulsePage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const translations = {
    title: t('pulse.title'),
    subtitle: t('pulse.subtitle'),
    howToUseTitle: t('pulse.howToUseTitle'),
    howToUseStep1Title: t('pulse.howToUseStep1Title'),
    howToUseStep1Desc: t('pulse.howToUseStep1Desc'),
    howToUseStep2Title: t('pulse.howToUseStep2Title'),
    howToUseStep2Desc: t('pulse.howToUseStep2Desc'),
    howToUseStep3Title: t('pulse.howToUseStep3Title'),
    howToUseStep3Desc: t('pulse.howToUseStep3Desc'),
    timeRangeSelector: {
      title: t('pulse.timeRangeSelector.title'),
      impactHorizonLabel: t('pulse.timeRangeSelector.impactHorizonLabel'),
      options: {
        sixMonths: t('pulse.timeRangeSelector.options.sixMonths'),
        sixMonthsDesc: t('pulse.timeRangeSelector.options.sixMonthsDesc'),
        oneYear: t('pulse.timeRangeSelector.options.oneYear'),
        oneYearDesc: t('pulse.timeRangeSelector.options.oneYearDesc'),
        fiveYears: t('pulse.timeRangeSelector.options.fiveYears'),
        fiveYearsDesc: t('pulse.timeRangeSelector.options.fiveYearsDesc')
      }
    },
    filters: {
      title: t('pulse.filters.title'),
      description: t('pulse.filters.description'),
      selectedLabel: t('pulse.filters.selectedLabel'),
      options: {
        ai: t('pulse.filters.options.ai'),
        aiDesc: t('pulse.filters.options.aiDesc'),
        cloud: t('pulse.filters.options.cloud'),
        cloudDesc: t('pulse.filters.options.cloudDesc'),
        iot: t('pulse.filters.options.iot'),
        iotDesc: t('pulse.filters.options.iotDesc'),
        security: t('pulse.filters.options.security'),
        securityDesc: t('pulse.filters.options.securityDesc'),
        blockchain: t('pulse.filters.options.blockchain'),
        blockchainDesc: t('pulse.filters.options.blockchainDesc')
      }
    },
    industryFilter: {
      title: t('pulse.industryFilter.title'),
      description: t('pulse.industryFilter.description'),
      placeholder: t('pulse.industryFilter.placeholder'),
      options: {
        all: t('pulse.industryFilter.options.all'),
        lifeInsurance: t('pulse.industryFilter.options.lifeInsurance'),
        technologyServices: t('pulse.industryFilter.options.technologyServices'),
        manufacturing: t('pulse.industryFilter.options.manufacturing'),
        healthcare: t('pulse.industryFilter.options.healthcare'),
        finance: t('pulse.industryFilter.options.finance'),
        energy: t('pulse.industryFilter.options.energy')
      }
    },
    maturityLevels: {
      title: t('pulse.maturityLevels.title'),
      established: t('pulse.maturityLevels.established'),
      maturing: t('pulse.maturityLevels.maturing'),
      growing: t('pulse.maturityLevels.growing'),
      emerging: t('pulse.maturityLevels.emerging'),
      descriptions: {
        established: t('pulse.maturityLevels.descriptions.established'),
        maturing: t('pulse.maturityLevels.descriptions.maturing'),
        growing: t('pulse.maturityLevels.descriptions.growing'),
        emerging: t('pulse.maturityLevels.descriptions.emerging')
      }
    },
    timelineTitle: t('pulse.timelineTitle'),
    timelineDescription: t('pulse.timelineDescription'),
    timelineIndustryLabel: t('pulse.timelineIndustryLabel'),
    noTechnologiesMessage: t('pulse.noTechnologiesMessage'),
    noTechnologiesIndustryMessage: t('pulse.noTechnologiesIndustryMessage'),
    resetFiltersLabel: t('pulse.resetFiltersLabel'),
    technologiesLabel: t('pulse.technologiesLabel'),
    technologyDetails: {
      businessImpactTab: t('pulse.technologyDetails.businessImpactTab'),
      technicalDetailsTab: t('pulse.technologyDetails.technicalDetailsTab'),
      industryImpactLabel: t('pulse.technologyDetails.industryImpactLabel'),
      industryImpactDescription: t('pulse.technologyDetails.industryImpactDescription'),
      businessApplicationsLabel: t('pulse.technologyDetails.businessApplicationsLabel'),
      useCasesLabel: t('pulse.technologyDetails.useCasesLabel'),
      keyPlayersLabel: t('pulse.technologyDetails.keyPlayersLabel'),
      predictedGrowthLabel: t('pulse.technologyDetails.predictedGrowthLabel')
    },
    technologies: {
      // Life Insurance Focused Technologies
      digitalHealthUnderwriting: {
        name: t('pulse.technologies.digitalHealthUnderwriting.name'),
        description: t('pulse.technologies.digitalHealthUnderwriting.description'),
        maturity: t('pulse.technologies.digitalHealthUnderwriting.maturity') as 'growing',
        category: t('pulse.technologies.digitalHealthUnderwriting.category'),
        adoptionRate: t('pulse.technologies.digitalHealthUnderwriting.adoptionRate'),
        industryImpact: {
          lifeInsurance: t('pulse.technologies.digitalHealthUnderwriting.industryImpact.lifeInsurance'),
          healthcare: t('pulse.technologies.digitalHealthUnderwriting.industryImpact.healthcare'),
          wellness: t('pulse.technologies.digitalHealthUnderwriting.industryImpact.wellness')
        },
        businessApplications: t('pulse.technologies.digitalHealthUnderwriting.businessApplications', { returnObjects: true }) as string[]
      },
      embeddedInsuranceAPIs: {
        name: t('pulse.technologies.embeddedInsuranceAPIs.name'),
        description: t('pulse.technologies.embeddedInsuranceAPIs.description'),
        maturity: t('pulse.technologies.embeddedInsuranceAPIs.maturity') as 'maturing',
        category: t('pulse.technologies.embeddedInsuranceAPIs.category'),
        adoptionRate: t('pulse.technologies.embeddedInsuranceAPIs.adoptionRate'),
        industryImpact: {
          lifeInsurance: t('pulse.technologies.embeddedInsuranceAPIs.industryImpact.lifeInsurance'),
          fintech: t('pulse.technologies.embeddedInsuranceAPIs.industryImpact.fintech'),
          retail: t('pulse.technologies.embeddedInsuranceAPIs.industryImpact.retail')
        },
        businessApplications: t('pulse.technologies.embeddedInsuranceAPIs.businessApplications', { returnObjects: true }) as string[]
      },
      parametricLifeProducts: {
        name: t('pulse.technologies.parametricLifeProducts.name'),
        description: t('pulse.technologies.parametricLifeProducts.description'),
        maturity: t('pulse.technologies.parametricLifeProducts.maturity') as 'growing',
        category: t('pulse.technologies.parametricLifeProducts.category'),
        adoptionRate: t('pulse.technologies.parametricLifeProducts.adoptionRate'),
        industryImpact: {
          lifeInsurance: t('pulse.technologies.parametricLifeProducts.industryImpact.lifeInsurance'),
          finance: t('pulse.technologies.parametricLifeProducts.industryImpact.finance'),
          healthcare: t('pulse.technologies.parametricLifeProducts.industryImpact.healthcare')
        },
        businessApplications: t('pulse.technologies.parametricLifeProducts.businessApplications', { returnObjects: true }) as string[]
      },
      // Technology Services Innovation
      composableBusinessArchitecture: {
        name: t('pulse.technologies.composableBusinessArchitecture.name'),
        description: t('pulse.technologies.composableBusinessArchitecture.description'),
        maturity: t('pulse.technologies.composableBusinessArchitecture.maturity') as 'maturing',
        category: t('pulse.technologies.composableBusinessArchitecture.category'),
        adoptionRate: t('pulse.technologies.composableBusinessArchitecture.adoptionRate'),
        industryImpact: {
          technologyServices: t('pulse.technologies.composableBusinessArchitecture.industryImpact.technologyServices'),
          finance: t('pulse.technologies.composableBusinessArchitecture.industryImpact.finance'),
          insurance: t('pulse.technologies.composableBusinessArchitecture.industryImpact.insurance')
        },
        businessApplications: t('pulse.technologies.composableBusinessArchitecture.businessApplications', { returnObjects: true }) as string[]
      },
      autonomousTestingPlatforms: {
        name: t('pulse.technologies.autonomousTestingPlatforms.name'),
        description: t('pulse.technologies.autonomousTestingPlatforms.description'),
        maturity: t('pulse.technologies.autonomousTestingPlatforms.maturity') as 'established',
        category: t('pulse.technologies.autonomousTestingPlatforms.category'),
        adoptionRate: t('pulse.technologies.autonomousTestingPlatforms.adoptionRate'),
        industryImpact: {
          technologyServices: t('pulse.technologies.autonomousTestingPlatforms.industryImpact.technologyServices'),
          software: t('pulse.technologies.autonomousTestingPlatforms.industryImpact.software'),
          manufacturing: t('pulse.technologies.autonomousTestingPlatforms.industryImpact.manufacturing')
        },
        businessApplications: t('pulse.technologies.autonomousTestingPlatforms.businessApplications', { returnObjects: true }) as string[]
      },
      edgeToCloudOrchestration: {
        name: t('pulse.technologies.edgeToCloudOrchestration.name'),
        description: t('pulse.technologies.edgeToCloudOrchestration.description'),
        maturity: t('pulse.technologies.edgeToCloudOrchestration.maturity') as 'growing',
        category: t('pulse.technologies.edgeToCloudOrchestration.category'),
        adoptionRate: t('pulse.technologies.edgeToCloudOrchestration.adoptionRate'),
        industryImpact: {
          technologyServices: t('pulse.technologies.edgeToCloudOrchestration.industryImpact.technologyServices'),
          manufacturing: t('pulse.technologies.edgeToCloudOrchestration.industryImpact.manufacturing'),
          energy: t('pulse.technologies.edgeToCloudOrchestration.industryImpact.energy')
        },
        businessApplications: t('pulse.technologies.edgeToCloudOrchestration.businessApplications', { returnObjects: true }) as string[]
      },
      // Advanced Machine Learning
      federatedLearningNetworks: {
        name: t('pulse.technologies.federatedLearningNetworks.name'),
        description: t('pulse.technologies.federatedLearningNetworks.description'),
        maturity: t('pulse.technologies.federatedLearningNetworks.maturity') as 'growing',
        category: t('pulse.technologies.federatedLearningNetworks.category'),
        adoptionRate: t('pulse.technologies.federatedLearningNetworks.adoptionRate'),
        industryImpact: {
          healthcare: t('pulse.technologies.federatedLearningNetworks.industryImpact.healthcare'),
          lifeInsurance: t('pulse.technologies.federatedLearningNetworks.industryImpact.lifeInsurance'),
          finance: t('pulse.technologies.federatedLearningNetworks.industryImpact.finance')
        },
        businessApplications: t('pulse.technologies.federatedLearningNetworks.businessApplications', { returnObjects: true }) as string[]
      },
      causalAISystems: {
        name: t('pulse.technologies.causalAISystems.name'),
        description: t('pulse.technologies.causalAISystems.description'),
        maturity: t('pulse.technologies.causalAISystems.maturity') as 'emerging',
        category: t('pulse.technologies.causalAISystems.category'),
        adoptionRate: t('pulse.technologies.causalAISystems.adoptionRate'),
        industryImpact: {
          lifeInsurance: t('pulse.technologies.causalAISystems.industryImpact.lifeInsurance'),
          healthcare: t('pulse.technologies.causalAISystems.industryImpact.healthcare'),
          manufacturing: t('pulse.technologies.causalAISystems.industryImpact.manufacturing')
        },
        businessApplications: t('pulse.technologies.causalAISystems.businessApplications', { returnObjects: true }) as string[]
      },
      neuralProcessAutomation: {
        name: t('pulse.technologies.neuralProcessAutomation.name'),
        description: t('pulse.technologies.neuralProcessAutomation.description'),
        maturity: t('pulse.technologies.neuralProcessAutomation.maturity') as 'growing',
        category: t('pulse.technologies.neuralProcessAutomation.category'),
        adoptionRate: t('pulse.technologies.neuralProcessAutomation.adoptionRate'),
        industryImpact: {
          insurance: t('pulse.technologies.neuralProcessAutomation.industryImpact.insurance'),
          technologyServices: t('pulse.technologies.neuralProcessAutomation.industryImpact.technologyServices'),
          manufacturing: t('pulse.technologies.neuralProcessAutomation.industryImpact.manufacturing')
        },
        businessApplications: t('pulse.technologies.neuralProcessAutomation.businessApplications', { returnObjects: true }) as string[]
      },
      // IoT & Manufacturing Technologies
      digitalTwinEcosystems: {
        name: t('pulse.technologies.digitalTwinEcosystems.name'),
        description: t('pulse.technologies.digitalTwinEcosystems.description'),
        maturity: t('pulse.technologies.digitalTwinEcosystems.maturity') as 'established',
        category: t('pulse.technologies.digitalTwinEcosystems.category'),
        adoptionRate: t('pulse.technologies.digitalTwinEcosystems.adoptionRate'),
        industryImpact: {
          manufacturing: t('pulse.technologies.digitalTwinEcosystems.industryImpact.manufacturing'),
          energy: t('pulse.technologies.digitalTwinEcosystems.industryImpact.energy'),
          insurance: t('pulse.technologies.digitalTwinEcosystems.industryImpact.insurance')
        },
        businessApplications: t('pulse.technologies.digitalTwinEcosystems.businessApplications', { returnObjects: true }) as string[]
      },
      autonomousSupplyNetworks: {
        name: t('pulse.technologies.autonomousSupplyNetworks.name'),
        description: t('pulse.technologies.autonomousSupplyNetworks.description'),
        maturity: t('pulse.technologies.autonomousSupplyNetworks.maturity') as 'growing',
        category: t('pulse.technologies.autonomousSupplyNetworks.category'),
        adoptionRate: t('pulse.technologies.autonomousSupplyNetworks.adoptionRate'),
        industryImpact: {
          manufacturing: t('pulse.technologies.autonomousSupplyNetworks.industryImpact.manufacturing'),
          energy: t('pulse.technologies.autonomousSupplyNetworks.industryImpact.energy'),
          technologyServices: t('pulse.technologies.autonomousSupplyNetworks.industryImpact.technologyServices')
        },
        businessApplications: t('pulse.technologies.autonomousSupplyNetworks.businessApplications', { returnObjects: true }) as string[]
      },
      greenHydrogenIntegration: {
        name: t('pulse.technologies.greenHydrogenIntegration.name'),
        description: t('pulse.technologies.greenHydrogenIntegration.description'),
        maturity: t('pulse.technologies.greenHydrogenIntegration.maturity') as 'emerging',
        category: t('pulse.technologies.greenHydrogenIntegration.category'),
        adoptionRate: t('pulse.technologies.greenHydrogenIntegration.adoptionRate'),
        industryImpact: {
          energy: t('pulse.technologies.greenHydrogenIntegration.industryImpact.energy'),
          manufacturing: t('pulse.technologies.greenHydrogenIntegration.industryImpact.manufacturing'),
          transportation: t('pulse.technologies.greenHydrogenIntegration.industryImpact.transportation')
        },
        businessApplications: t('pulse.technologies.greenHydrogenIntegration.businessApplications', { returnObjects: true }) as string[]
      },
      // Security & Architecture
      meshArchitecturePlatforms: {
        name: t('pulse.technologies.meshArchitecturePlatforms.name'),
        description: t('pulse.technologies.meshArchitecturePlatforms.description'),
        maturity: t('pulse.technologies.meshArchitecturePlatforms.maturity') as 'maturing',
        category: t('pulse.technologies.meshArchitecturePlatforms.category'),
        adoptionRate: t('pulse.technologies.meshArchitecturePlatforms.adoptionRate'),
        industryImpact: {
          technologyServices: t('pulse.technologies.meshArchitecturePlatforms.industryImpact.technologyServices'),
          enterpriseArchitecture: t('pulse.technologies.meshArchitecturePlatforms.industryImpact.enterpriseArchitecture'),
          finance: t('pulse.technologies.meshArchitecturePlatforms.industryImpact.finance')
        },
        businessApplications: t('pulse.technologies.meshArchitecturePlatforms.businessApplications', { returnObjects: true }) as string[]
      },
      privacyPreservingAnalytics: {
        name: t('pulse.technologies.privacyPreservingAnalytics.name'),
        description: t('pulse.technologies.privacyPreservingAnalytics.description'),
        maturity: t('pulse.technologies.privacyPreservingAnalytics.maturity') as 'growing',
        category: t('pulse.technologies.privacyPreservingAnalytics.category'),
        adoptionRate: t('pulse.technologies.privacyPreservingAnalytics.adoptionRate'),
        industryImpact: {
          lifeInsurance: t('pulse.technologies.privacyPreservingAnalytics.industryImpact.lifeInsurance'),
          healthcare: t('pulse.technologies.privacyPreservingAnalytics.industryImpact.healthcare'),
          finance: t('pulse.technologies.privacyPreservingAnalytics.industryImpact.finance')
        },
        businessApplications: t('pulse.technologies.privacyPreservingAnalytics.businessApplications', { returnObjects: true }) as string[]
      },
      quantumSafeCryptography: {
        name: t('pulse.technologies.quantumSafeCryptography.name'),
        description: t('pulse.technologies.quantumSafeCryptography.description'),
        maturity: t('pulse.technologies.quantumSafeCryptography.maturity') as 'emerging',
        category: t('pulse.technologies.quantumSafeCryptography.category'),
        adoptionRate: t('pulse.technologies.quantumSafeCryptography.adoptionRate'),
        industryImpact: {
          finance: t('pulse.technologies.quantumSafeCryptography.industryImpact.finance'),
          lifeInsurance: t('pulse.technologies.quantumSafeCryptography.industryImpact.lifeInsurance'),
          technologyServices: t('pulse.technologies.quantumSafeCryptography.industryImpact.technologyServices')
        },
        businessApplications: t('pulse.technologies.quantumSafeCryptography.businessApplications', { returnObjects: true }) as string[]
      }
    },
    relevanceMeter: {
      title: t('pulse.relevanceMeter.title'),
      description: t('pulse.relevanceMeter.description'),
      companySize: {
        title: t('pulse.relevanceMeter.companySize.title'),
        description: t('pulse.relevanceMeter.companySize.description'),
        options: {
          startup: t('pulse.relevanceMeter.companySize.options.startup'),
          smb: t('pulse.relevanceMeter.companySize.options.smb'),
          enterprise: t('pulse.relevanceMeter.companySize.options.enterprise')
        }
      },
      industry: {
        title: t('pulse.relevanceMeter.industry.title'),
        description: t('pulse.relevanceMeter.industry.description'),
        selectPlaceholder: t('pulse.relevanceMeter.industry.selectPlaceholder'),
        options: {
          healthcare: t('pulse.relevanceMeter.industry.options.healthcare'),
          finance: t('pulse.relevanceMeter.industry.options.finance'),
          retail: t('pulse.relevanceMeter.industry.options.retail'),
          manufacturing: t('pulse.relevanceMeter.industry.options.manufacturing'),
          technology: t('pulse.relevanceMeter.industry.options.technology')
        }
      },
      relevanceScore: {
        title: t('pulse.relevanceMeter.relevanceScore.title'),
        lowLabel: t('pulse.relevanceMeter.relevanceScore.lowLabel'),
        highLabel: t('pulse.relevanceMeter.relevanceScore.highLabel'),
        descriptions: {
          high: t('pulse.relevanceMeter.relevanceScore.descriptions.high'),
          good: t('pulse.relevanceMeter.relevanceScore.descriptions.good'),
          moderate: t('pulse.relevanceMeter.relevanceScore.descriptions.moderate'),
          low: t('pulse.relevanceMeter.relevanceScore.descriptions.low')
        }
      },
      cta: t('pulse.relevanceMeter.cta'),
      ctaDescription: t('pulse.relevanceMeter.ctaDescription'),
      ctaTitle: t('pulse.relevanceMeter.ctaTitle'),
      footerText: t('pulse.relevanceMeter.footerText')
    }
  };

  return <PulseSection translations={translations} />;
}
