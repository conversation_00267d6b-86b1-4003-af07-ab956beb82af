// src/lib/i18n.ts
import { readFileSync } from 'fs';
import { createInstance } from 'i18next'
import resourcesToBackend from 'i18next-resources-to-backend'
import path from 'path';

export const LOCALES = ['en', 'sl', 'de', 'hr', 'fr', 'it', 'es', 'zh']
export const DEFAULT_LOCALE = 'en'

console.log('i18n module loaded | Default locale:', DEFAULT_LOCALE); // 👈 Initial load

export const getOptions = (locale = DEFAULT_LOCALE, namespaces = ['language']) => {
  console.log('i18n options requested | Locale:', locale, '| Namespaces:', namespaces); // 👈 Options debug
  return {
    debug: process.env.NODE_ENV === 'development',
    supportedLngs: LOCALES,
    fallbackLng: DEFAULT_LOCALE,
    lng: locale,
    fallbackNS: 'language',
    defaultNS: 'language',
    ns: namespaces,
    returnObjects: true, // Enable returnObjects to allow returning arrays and objects
    interpolation: {
      escapeValue: false,
    },
  }
}

const loadLocaleFrom = async (locale: string, namespace: string) => {
  console.log('Loading translations | Locale:', locale, '| Namespace:', namespace); // 👈 Translation fetch start
  try {
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH || ''
    const url = `${basePath}/locales/${locale}/${namespace}.json`
    console.log('Fetching translations from:', url); // 👈 Debug URL
    if (typeof window === 'undefined') {
      // Server-side: Direct file read
      const filePath = path.join(process.cwd(), 'public/locales', locale, `${namespace}.json`);
      return JSON.parse(readFileSync(filePath, 'utf-8'));
    }
    else {
      const response = await fetch(url)
      console.log('Translation response status:', response.status); // 👈 Response status
      if (!response.ok) throw new Error(`Failed to fetch translations: ${response.status}`)

      const data = await response.json()
      console.log('Successfully loaded translations:', { locale, namespace, data: Object.keys(data) }); // 👈 Success (logs keys only)
      return data
    }

  } catch (e) {
    console.error(`🚨 Failed to load ${namespace} for ${locale}`, e) // 👈 Enhanced error
    console.error(`Check if the file exists at: public/locales/${locale}/${namespace}.json`)
    // Return an empty object but log a more specific error
    return {}
  }
}

export async function createI18nInstance(locale: string, namespaces: string[]) {
  console.log('Creating i18n instance | Locale:', locale, '| Namespaces:', namespaces); // 👈 Instance creation
  const instance = createInstance()

  await instance
    .use(resourcesToBackend(loadLocaleFrom))
    .init(getOptions(locale, namespaces))

  console.log('i18n instance ready:', { locale, namespaces }); // 👈 Initialization complete
  return instance
}

export async function getServerTranslations(locale: string, namespaces: string[]) {
  console.log('getServerTranslations called | Locale:', locale, '| Namespaces:', namespaces); // 👈 Server-side entry
  const i18n = await createI18nInstance(locale, namespaces)
  const t = i18n.getFixedT(locale, namespaces[0])

  console.log('Translation function (t) ready for:', locale); // 👈 Confirmation
  return { t }
}