{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Next.js with Turbopack", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev:turbo"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "port": 9230}, {"type": "node", "request": "launch", "name": "Next.js with Webpack", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev:webpack"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "port": 9231}, {"type": "node", "request": "launch", "name": "Setup Confi<PERSON>", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev:setup-config"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "port": 9232}, {"type": "chrome", "request": "launch", "name": "Debug in Chrome", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack://_N_E/*": "${workspaceFolder}/*", "webpack://app/*": "${workspaceFolder}/*"}}], "compounds": [{"name": "Full Stack Debug (Turbopack)", "configurations": ["Next.js with Turbopack", "Debug in Chrome"]}, {"name": "Full Stack Debug (Webpack)", "configurations": ["Next.js with Webpack", "Debug in Chrome"]}]}