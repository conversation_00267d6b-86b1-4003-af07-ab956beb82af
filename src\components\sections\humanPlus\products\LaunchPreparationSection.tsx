'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ProcessDetails from './ProcessDetails';

interface LaunchPreparationSectionProps {
  translations?: {
    managementPhases?: {
      launchPreparation?: {
        title?: string;
        description?: string;
        processes?: {
          marketingStrategy?: string;
          launchPreparation?: string;
          communicationPlan?: string;
          organizationalChart?: string;
          changeManagement?: string;
        };
      };
    };
    components?: {
      keyProcesses?: string;
      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
      processDetails?: {
        closeButton?: string;
        implementationBenefits?: string;
        contentNotAvailable?: string;
        defaultFallbackDescription?: string;
        defaultBenefits?: string[];
        defaultFeatures?: string[];
      };
    };
  };
}

export default function LaunchPreparationSection({ translations }: LaunchPreparationSectionProps) {
  const [selectedProcess, setSelectedProcess] = useState<string | null>(null);
  const [forceVisible, setForceVisible] = useState(false);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Force visibility after 2 seconds as fallback
  useEffect(() => {
    const timer = setTimeout(() => {
      setForceVisible(true);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  const handleProcessClick = (process: string) => {
    setSelectedProcess(process === selectedProcess ? null : process);
  };

  const processes = [
    translations?.managementPhases?.launchPreparation?.processes?.marketingStrategy || 'Marketing Strategy',
    translations?.managementPhases?.launchPreparation?.processes?.launchPreparation || 'Launch Preparation',
    translations?.managementPhases?.launchPreparation?.processes?.communicationPlan || 'Communication Plan',
    translations?.managementPhases?.launchPreparation?.processes?.organizationalChart || 'Organizational Chart'
  ];

  const phaseVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5
      }
    })
  };

  return (
    <section ref={ref} className="relative w-full pt-6 pb-16 overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="bg-gray-900/60 backdrop-blur-sm rounded-lg border border-gray-800 p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row items-start gap-6">
            <div className="text-cyan-400 text-3xl flex-shrink-0">
              🚀
            </div>

            <div className="flex-grow">
              <h3 className="text-2xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
                {translations?.managementPhases?.launchPreparation?.title || 'Launch Preparation & Execution'}
              </h3>
              <p className="text-gray-300 text-lg mb-6">
                {translations?.managementPhases?.launchPreparation?.description || 'Prepare for successful product launches with comprehensive planning, marketing strategies, and organizational alignment. Ensure all stakeholders are prepared for change and communication plans are in place for maximum market impact.'}
              </p>

            <h4 className="text-xl font-semibold mb-4 text-cyan-400">
              {translations?.components?.keyProcesses || 'Key Processes'}
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {processes.map((process, idx) => (
                <motion.div
                  key={process}
                  className={`bg-gray-800/50 border ${
                    selectedProcess === process
                      ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                      : 'border-purple-500/20'
                  } rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-800/80 relative`}
                  custom={idx}
                  variants={phaseVariants}
                  initial="hidden"
                  animate={isIntersecting || forceVisible ? "visible" : "hidden"}
                  onClick={() => handleProcessClick(process)}
                  whileHover={{ scale: 1.02 }}
                >
                  <span className="text-white font-medium">{process}</span>
                </motion.div>
              ))}
            </div>

            {/* Process Details Modal */}
            {selectedProcess && (
              <ProcessDetails
                process={selectedProcess}
                translations={translations}
                onClose={() => setSelectedProcess(null)}
              />
            )}
          </div>
        </div>
      </motion.div>
      </div>
    </section>
  );
}
