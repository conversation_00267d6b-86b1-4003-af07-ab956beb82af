// setup-config.ts
import * as fs from 'fs';
import * as path from 'path';

// Determine which config to use based on environment variable
const useTurbopack: boolean = process.env.USE_TURBOPACK === 'true';
const configSource: string = useTurbopack ? 
  path.join(__dirname, 'next.config.turbo.ts') : 
  path.join(__dirname, 'next.config.webpack.ts');

const configDest: string = path.join(__dirname, 'next.config.ts');

try {
  // Read the source config file
  const configContent: string = fs.readFileSync(configSource, 'utf8');
  
  // Write to the destination config file
  fs.writeFileSync(configDest, configContent);
  
  console.log(`Successfully configured Next.js with ${useTurbopack ? 'Turbopack' : 'Webpack'} settings.`);
} catch (error: unknown) {
  console.error('Error setting up Next.js configuration:', error);
  process.exit(1);
}
