// ZoomControls.tsx
'use client';
import React from 'react';

interface ZoomControlsTranslations {
  zoomIn: string;
  zoomOut: string;
  reset: string;
}

interface ZoomControlsProps {
  scale: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;
  translations: ZoomControlsTranslations;
}

const ZoomControls: React.FC<ZoomControlsProps> = ({
  scale,
  onZoomIn,
  onZoomOut,
  onReset,
  translations
}) => {
  return (
    <div className="flex flex-col gap-2 bg-gray-800/80 backdrop-blur-sm rounded-lg p-2 border border-gray-700 shadow-lg">
      <button
        onClick={onZoomIn}
        className="p-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
        title={translations.zoomIn}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
      </button>
      <div className="text-xs text-center text-gray-300">
        {Math.round(scale * 100)}%
      </div>
      <button
        onClick={onZoomOut}
        className="p-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
        title={translations.zoomOut}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"></path>
        </svg>
      </button>
      <button
        onClick={onReset}
        className="p-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors mt-2"
        title={translations.reset}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
        </svg>
      </button>
    </div>
  );
};

export default ZoomControls;