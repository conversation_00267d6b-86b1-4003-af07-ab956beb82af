import ContactPortal from '@/components/sections/ContactPortal';
import { getServerTranslations } from '@/lib/i18n';

export default async function ContactPortalSection({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const contactPortalTranslations = {
    title: t('contact.title'),
    subtitle: t('contact.subtitle'),
    placeholder: t('contact.placeholder'),
    submissionOptions: {
      type: t('contact.submissionOptions.type'),
      speak: t('contact.submissionOptions.speak'),
      show: t('contact.submissionOptions.show')
    },
    namePlaceholder: t('contact.namePlaceholder'),
    emailPlaceholder: t('contact.emailPlaceholder'),
    messagePlaceholder: t('contact.messagePlaceholder'),
    submitButton: t('contact.submitButton'),
    successMessage: t('contact.successMessage'),
    errorMessage: t('contact.errorMessage'),
    servicesLabel: t('contact.servicesLabel'),
    services: [
      { value: 'ml', label: t('contact.services.0.label') },
      { value: 'architecture', label: t('contact.services.1.label') },
      { value: 'consulting', label: t('contact.services.2.label') },
      { value: 'lifeinsurance', label: t('contact.services.3.label') },
      { value: 'other', label: t('contact.services.4.label') }
    ]
  };

  return <ContactPortal translations={contactPortalTranslations} />;
}