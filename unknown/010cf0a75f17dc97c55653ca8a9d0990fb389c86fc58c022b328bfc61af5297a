'use client';

import React, { useEffect, useRef } from 'react';

interface DataStreamsProps {
  density?: 'low' | 'medium' | 'high';
  speed?: 'slow' | 'medium' | 'fast';
  primaryColor?: string;
  secondaryColor?: string;
  direction?: 'left-to-right' | 'right-to-left' | 'top-to-bottom' | 'bottom-to-top' | 'diagonal';
  className?: string;
}

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  opacity: number;
  length: number;
  initialX: number;
  initialY: number;
}

const DataStreams: React.FC<DataStreamsProps> = ({
  density = 'medium',
  speed = 'medium',
  primaryColor = '#00f2fe',
  secondaryColor = '#4facfe',
  direction = 'left-to-right',
  className = ''
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number>(0);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    
    if (!canvas || !container) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas size
    const resizeCanvas = () => {
      const dpr = window.devicePixelRatio || 1;
      const rect = container.getBoundingClientRect();
      
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;
      
      ctx.scale(dpr, dpr);
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Particle density based on prop
    const densityMap = {
      low: 30,
      medium: 70,
      high: 100
    };
    
    const particleCount = densityMap[density];
    
    // Speed multiplier based on prop
    const speedMap = {
      slow: 0.5,
      medium: 1,
      fast: 2
    };
    
    const speedMultiplier = speedMap[speed];
    
    // Set direction vectors
    let directionVector = { x: 1, y: 0 }; // default left-to-right
    
    switch (direction) {
      case 'left-to-right':
        directionVector = { x: 1, y: 0 };
        break;
      case 'right-to-left':
        directionVector = { x: -1, y: 0 };
        break;
      case 'top-to-bottom':
        directionVector = { x: 0, y: 1 };
        break;
      case 'bottom-to-top':
        directionVector = { x: 0, y: -1 };
        break;
      case 'diagonal':
        directionVector = { x: 1, y: 1 };
        break;
    }
    
    // Initialize particles
    const initParticles = () => {
      const particles: Particle[] = [];
      const rect = canvas.getBoundingClientRect();
      
      for (let i = 0; i < particleCount; i++) {
        let x, y;
        
        // Position based on direction
        if (direction === 'left-to-right') {
          x = Math.random() * rect.width * 0.25;
          y = Math.random() * rect.height;
        } else if (direction === 'right-to-left') {
          x = rect.width - (Math.random() * rect.width * 0.25);
          y = Math.random() * rect.height;
        } else if (direction === 'top-to-bottom') {
          x = Math.random() * rect.width;
          y = Math.random() * rect.height * 0.25;
        } else if (direction === 'bottom-to-top') {
          x = Math.random() * rect.width;
          y = rect.height - (Math.random() * rect.height * 0.25);
        } else {
          // Diagonal
          x = Math.random() * rect.width * 0.25;
          y = Math.random() * rect.height * 0.25;
        }
        
        const size = Math.random() * 1.5 + 0.5;
        const color = Math.random() > 0.3 ? primaryColor : secondaryColor;
        const length = Math.random() * 40 + 20;
        
        particles.push({
          x,
          y,
          initialX: x,
          initialY: y,
          size,
          speedX: directionVector.x * (Math.random() * 0.5 + 0.5) * speedMultiplier,
          speedY: directionVector.y * (Math.random() * 0.5 + 0.5) * speedMultiplier,
          color,
          opacity: Math.random() * 0.7 + 0.3,
          length
        });
      }
      
      particlesRef.current = particles;
    };
    
    // Reset particle position when it goes out of bounds
    const resetParticle = (particle: Particle) => {
      const rect = canvas.getBoundingClientRect();
      
      if (direction === 'left-to-right' && particle.x - particle.length > rect.width) {
        particle.x = particle.initialX;
        particle.y = Math.random() * rect.height;
      } else if (direction === 'right-to-left' && particle.x + particle.length < 0) {
        particle.x = particle.initialX;
        particle.y = Math.random() * rect.height;
      } else if (direction === 'top-to-bottom' && particle.y - particle.length > rect.height) {
        particle.x = Math.random() * rect.width;
        particle.y = particle.initialY;
      } else if (direction === 'bottom-to-top' && particle.y + particle.length < 0) {
        particle.x = Math.random() * rect.width;
        particle.y = particle.initialY;
      } else if (direction === 'diagonal' && 
                (particle.x - particle.length > rect.width || 
                 particle.y - particle.length > rect.height)) {
        particle.x = particle.initialX;
        particle.y = particle.initialY;
      }
    };
    
    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particlesRef.current.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Check if particle needs to be reset
        resetParticle(particle);
        
        // Draw data stream
        ctx.beginPath();
        
        // Create gradient for trail effect
        const gradient = ctx.createLinearGradient(
          particle.x - (directionVector.x * particle.length),
          particle.y - (directionVector.y * particle.length),
          particle.x,
          particle.y
        );
        
        gradient.addColorStop(0, 'rgba(0,0,0,0)');
        gradient.addColorStop(1, particle.color);
        
        ctx.strokeStyle = gradient;
        ctx.lineWidth = particle.size;
        ctx.globalAlpha = particle.opacity;
        ctx.moveTo(particle.x - (directionVector.x * particle.length), 
                  particle.y - (directionVector.y * particle.length));
        ctx.lineTo(particle.x, particle.y);
        ctx.stroke();
        
        // Draw particle head
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size * 1.5, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
        
        ctx.globalAlpha = 1;
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    initParticles();
    animate();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationRef.current);
    };
  }, [density, speed, primaryColor, secondaryColor, direction]);
  
  return (
    <div 
      ref={containerRef} 
      className={`data-streams relative overflow-hidden ${className}`}
    >
      <canvas 
        ref={canvasRef} 
        className="absolute inset-0 w-full h-full"
      />
    </div>
  );
};

export default DataStreams;