'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface NavItem {
  id: string;
  icon: React.ReactNode;
  label: string;
  path: string;
}

interface PulsingOrbProps {
  className?: string;
  size?: number;
  color?: string;
  pulseColor?: string;
  navItems?: NavItem[];
  onNavItemClick?: (item: NavItem) => void;
  translations: {
    navigation: {
      labs: string;
      architectures: string;
      futures: string;
      humanPlus: string;
      pulse: string;
    }
  };
}

const PulsingOrb: React.FC<PulsingOrbProps> = ({
  className = '',
  size = 50,
  color = '#00FFFF',
  pulseColor = 'rgba(0, 255, 255, 0.4)',
  navItems = [],
  onNavItemClick,
  translations
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const orbRef = useRef<HTMLDivElement>(null);

  // Default navigation items if none provided
  const defaultNavItems: NavItem[] = [
    {
      id: 'labs',
      icon: <span className="nav-icon">🔬</span>,
      label: translations.navigation.labs,
      path: '/labs'
    },
    {
      id: 'architectures',
      icon: <span className="nav-icon">🏛️</span>,
      label: translations.navigation.architectures,
      path: '/architectures'
    },
    {
      id: 'futures',
      icon: <span className="nav-icon">🌌</span>,
      label: translations.navigation.futures,
      path: '/futures'
    },
    {
      id: 'human-plus',
      icon: <span className="nav-icon">👤</span>,
      label: translations.navigation.humanPlus,
      path: '/human-plus'
    },
    {
      id: 'pulse',
      icon: <span className="nav-icon">🌀</span>,
      label: translations.navigation.pulse,
      path: '/'
    }
  ];

  const items = navItems.length > 0 ? navItems : defaultNavItems;

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (orbRef.current && !orbRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle navigation item click
  const handleItemClick = (item: NavItem) => {
    if (onNavItemClick) {
      onNavItemClick(item);
    }
    setIsExpanded(false);
  };

  return (
    <div
      ref={orbRef}
      className={`fixed bottom-8 right-8 z-50 ${className}`}
      style={{ width: size, height: size }}
    >
      {/* Main Orb */}
      <motion.div
        className="cursor-pointer relative flex items-center justify-center rounded-full shadow-lg"
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: 'rgba(10, 10, 20, 0.8)',
          border: `2px solid ${color}`,
          boxShadow: `0 0 15px ${pulseColor}, 0 0 30px ${pulseColor}`,
        }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <motion.div
          className="absolute inset-0 rounded-full"
          animate={{
            boxShadow: [
              `0 0 5px ${pulseColor}`,
              `0 0 25px ${pulseColor}`,
              `0 0 5px ${pulseColor}`
            ],
            scale: [1, 1.05, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <span className="text-white text-xl">🌀</span>
      </motion.div>

      {/* Navigation Menu */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            className="absolute bottom-full right-0 mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            <div className="bg-black/80 rounded-lg p-2 backdrop-blur-lg border border-gray-800">
              <ul className="space-y-2">
                {items.map((item, index) => (
                  <motion.li
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.05 * index }}
                    className="transition-all duration-300"
                  >
                    <button
                      onClick={() => handleItemClick(item)}
                      className="flex items-center space-x-3 w-full px-4 py-2 rounded-md hover:bg-gray-800 text-white transition-colors"
                    >
                      <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700">
                        {item.icon}
                      </div>
                      <span className="font-medium">{item.label}</span>
                    </button>
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PulsingOrb;