// src/context/AnimationContext.tsx
'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

type AnimationContextType = {
  mousePosition: { x: number; y: number }
  scrollProgress: number
  isReducedMotion: boolean
  activeSection: string
  setActiveSection: (section: string) => void
}

const defaultContext: AnimationContextType = {
  mousePosition: { x: 0, y: 0 },
  scrollProgress: 0,
  isReducedMotion: false,
  activeSection: 'hero',
  setActiveSection: () => {}
}

const AnimationContext = createContext<AnimationContextType>(defaultContext)

export const useAnimationContext = () => useContext(AnimationContext)

export const AnimationContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [scrollProgress, setScrollProgress] = useState(0)
  const [isReducedMotion, setIsReducedMotion] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')

  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setIsReducedMotion(mediaQuery.matches)

    const mediaQueryListener = () => setIsReducedMotion(mediaQuery.matches)
    mediaQuery.addEventListener('change', mediaQueryListener)

    // Track mouse movement for interactive elements
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    // Track scroll position for animations
    const handleScroll = () => {
      const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = scrollHeight > 0 ? window.scrollY / scrollHeight : 0
      setScrollProgress(progress)
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('scroll', handleScroll)
      mediaQuery.removeEventListener('change', mediaQueryListener)
    }
  }, [])

  return (
    <AnimationContext.Provider
      value={{
        mousePosition,
        scrollProgress,
        isReducedMotion,
        activeSection,
        setActiveSection
      }}
    >
      {children}
    </AnimationContext.Provider>
  )
}