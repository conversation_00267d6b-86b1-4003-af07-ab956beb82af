'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Button from '../ui/Button';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface ContactPortalProps {
  translations?: {
    title: string;
    subtitle: string;
    namePlaceholder: string;
    emailPlaceholder: string;
    messagePlaceholder: string;
    submitButton: string;
    successMessage: string;
    errorMessage: string;
    servicesLabel: string;
    services: Array<{ value: string; label: string }>;
  };
}

// Empty default translations - all translations should come from props
const defaultTranslations = {
  title: "",
  subtitle: "",
  namePlaceholder: "",
  emailPlaceholder: "",
  messagePlaceholder: "",
  submitButton: "",
  successMessage: "",
  errorMessage: "",
  servicesLabel: "",
  services: [
    { value: "", label: "" }
  ]
};

const ContactPortal: React.FC<ContactPortalProps> = ({ translations = defaultTranslations }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    service: translations.services[0].value,
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const { ref: formRef, isIntersecting: isVisible } = useIntersectionObserver({ threshold: 0.2 });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          subject: `New inquiry about: ${
            translations.services.find(s => s.value === formData.service)?.label || formData.service
          }`
        }),
      });

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          service: translations.services[0].value,
          message: ''
        });
      } else {
        setSubmitStatus('error');
      }
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);

      // Reset status after 5 seconds
      setTimeout(() => {
        setSubmitStatus('idle');
      }, 5000);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section
      ref={formRef}
      className="relative min-h-screen bg-black pt-32 pb-20 overflow-hidden"
    >
      {/* Background elements - similar to other sections */}
      <div className="absolute inset-0 bg-gradient-to-r from-black via-black/70 to-transparent z-0"></div>
      {/* Removed neural-gradient that might be causing lines */}

      {/* Removed all decorative lines */}

      {/* Content */}
      <motion.div
        className="container mx-auto px-4 relative z-10"
        initial="hidden"
        animate={isVisible ? 'visible' : 'hidden'}
        variants={formVariants}
      >
        <motion.div variants={itemVariants} className="text-center mb-12 relative z-20">
          {/* Added subtle background to ensure text readability */}
          <div className="absolute inset-0 bg-black/40 blur-xl rounded-3xl -z-10"></div>

          <h2 className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-500 mb-6 drop-shadow-md">
            {translations.title}
          </h2>
          <p className="text-lg md:text-xl font-normal text-gray-300 max-w-2xl mx-auto drop-shadow">{translations.subtitle}</p>
        </motion.div>

        <div className="max-w-2xl mx-auto">
          <motion.div
            variants={itemVariants}
            className="bg-gradient-to-br from-gray-900 to-black/80 backdrop-blur-sm border border-gray-700/70 rounded-lg p-8 shadow-md"
            whileHover={{ boxShadow: "0 10px 30px -10px rgba(0, 200, 255, 0.2)" }}
          >
            {submitStatus === 'success' ? (
              <div className="text-center py-8">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-500/20 border border-green-500/30 mb-4">
                  <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <h3 className="text-xl md:text-2xl font-semibold text-white mb-2">{translations.successMessage}</h3>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder={translations.namePlaceholder}
                      required
                      className="w-full bg-gray-800/40 border border-gray-600/70 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-500/40 rounded-lg px-4 py-3 text-white placeholder-gray-400 outline-none transition-all"
                    />
                  </div>
                  <div>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder={translations.emailPlaceholder}
                      required
                      className="w-full bg-gray-800/40 border border-gray-600/70 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-500/40 rounded-lg px-4 py-3 text-white placeholder-gray-400 outline-none transition-all"
                    />
                  </div>
                </div>

                <div>
                  <select
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    required
                    className="w-full bg-gray-800/40 border border-gray-600/70 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-500/40 rounded-lg px-4 py-3 text-white outline-none transition-all appearance-none"
                    style={{
                      backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%238B9CB6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundPosition: 'right 1rem center',
                      backgroundSize: '1em'
                    }}
                  >
                    {translations.services.map((service) => (
                      <option key={service.value} value={service.value}>
                        {service.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder={translations.messagePlaceholder}
                    required
                    rows={4}
                    className="w-full bg-gray-800/40 border border-gray-600/70 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-500/40 rounded-lg px-4 py-3 text-white placeholder-gray-400 outline-none transition-all resize-none"
                  ></textarea>
                </div>

                {submitStatus === 'error' && (
                  <div className="bg-red-500/20 border border-red-500/30 text-red-400 p-3 rounded-lg text-center">
                    {translations.errorMessage}
                  </div>
                )}

                <div className="text-center">
                  <Button
                    variant="secondary"
                    size="md"
                    hasGlow={true}
                    disabled={isSubmitting}
                    className="w-full md:w-auto"
                    onClick={(e: React.MouseEvent) => {
                      e.preventDefault();
                      if (!isSubmitting) {
                        const event = { preventDefault: () => {} } as React.FormEvent;
                        handleSubmit(event);
                      }
                    }}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {translations.submitButton}
                      </span>
                    ) : translations.submitButton}
                  </Button>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </motion.div>

      {/* Spacer to push footer down */}
      <div className="h-40 md:h-60"></div>
    </section>
  );
};

export default ContactPortal;