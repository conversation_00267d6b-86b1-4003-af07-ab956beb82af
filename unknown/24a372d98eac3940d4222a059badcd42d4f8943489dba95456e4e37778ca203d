'use client';

import { motion } from 'framer-motion';
import { useEffect, useState, useRef } from 'react';
import Button from '../ui/Button';
import AnimatedBackground from '../ui/AnimatedBackground';
import { useAnimationContext } from '@/context/AnimationContext';
import DynamicMindMap from '../ui/DynamicMindMap';

// Define the type for translations
type HeroTranslations = {
  title: string;
  subtitle: string;
  cta: string;
  mindMap?: {
    title: string;
    expandView: string;
    defaultView: string;
    zoomIn: string;
    zoomOut: string;
    close: string;
    keyFeatures: string;
    keyBenefits: string;
    learnMore: string;
    nodes: Array<{
      id: string;
      name: string;
      x: number;
      y: number;
      icon?: string;
      color?: string;
      iconColor?: string;
      navLink?: string;
    }>;
    details: Record<string, {
      title: string;
      description: string;
      icon: string;
      features: string[];
      benefits: string[];
    }>;
    detailNodes: Array<{
      id: string;
      name: string;
      x: number;
      y: number;
      color: string;
      iconColor: string;
    }>;
  };
};

// Default mind map translations as fallback
const defaultMindMapTranslations = {
  title: "Elysian Services",
  expandView: "Expand View",
  defaultView: "Default View",
  zoomIn: "Zoom In",
  zoomOut: "Zoom Out",
  close: "Close",
  keyFeatures: "Key Features",
  keyBenefits: "Key Benefits",
  learnMore: "Learn More",
  nodes: [],
  details: {},
  detailNodes: []
};

export default function HeroSection({ translations }: { translations: HeroTranslations }) {
  const { setActiveSection } = useAnimationContext();
  const [interactiveMode, setInteractiveMode] = useState(true);
  const [isMindMapOpen, setIsMindMapOpen] = useState(false);
  const heroSectionRef = useRef<HTMLDivElement>(null);
  
  // Use translations.mindMap if available, otherwise use default
  const mindMapTranslations = translations.mindMap || defaultMindMapTranslations;
    
  const handleCloseMindMap = () => {
    setIsMindMapOpen(false);
    setInteractiveMode(true);
  };

  useEffect(() => {
    setActiveSection('hero');
    return () => {
      setActiveSection(''); // Use an empty string instead of null
    };
  }, [setActiveSection]);

  // Function to handle clicks on the hero section (outside the mind map)
  //useEffect(() => {
    // Only add listener when mind map is open
    // if (!isMindMapOpen) return;

    // const handleOutsideClick = (e: MouseEvent) => {
    //   // If the mindmap is open and we click outside, don't do anything
    //   // The mind map component will handle its own outside clicks
    //   e.stopPropagation();
    // };

    // Add event listener to prevent hero section clicks from closing mind map unexpectedly
    // const heroSectionElement = heroSectionRef.current;
    //if (heroSectionElement) {
    //  heroSectionElement.addEventListener('click', handleOutsideClick as any);
    // }

    // Clean up
    //return () => {
    //  if (heroSectionElement) {
    //    heroSectionElement.removeEventListener('click', handleOutsideClick as any);
    //  }
    // };
  // }, [isMindMapOpen]);

  // Function to open the mind map when CTA button is clicked
  const handleStartEvolution = () => {
    //e.preventDefault();
    //e.stopPropagation();
    console.log('Opening mind map...');
    setIsMindMapOpen(true);

    // Temporarily disable interactive mode for the background when mind map is open
    setInteractiveMode(false);
  };

  return (
    <section
      ref={heroSectionRef}
      className="relative h-screen w-full overflow-hidden bg-black"
      style={{ paddingTop: 'var(--header-height)' }}
    >
      {/* Overlay gradient for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-black via-black to-transparent z-0" style={{ '--tw-gradient-via-opacity': '0.7' } as React.CSSProperties}></div>

      {/* Animated Background - takes whole screen */}
      <div className="absolute inset-0">
        <AnimatedBackground
          type="neural"
          density="high"
          color="#00f2fe"
          secondaryColor="#4facfe"
          speed="medium"
          interactive={interactiveMode}
        />
      </div>

      {/* Content container with text and CTA */}
      <div className="relative z-10 flex flex-col items-start justify-center h-full max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <motion.div
          className="w-full md:w-3/5 lg:w-1/2"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <motion.h1
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-600 leading-tight mb-4 sm:mb-6 md:mb-8"
          >
            {translations.title}
          </motion.h1>

          <motion.p
            className="text-gray-300 text-sm sm:text-base md:text-lg lg:text-xl mb-6 sm:mb-8 md:mb-12 max-w-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {translations.subtitle}
          </motion.p>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Button
              withArrow
              variant="secondary"
              size="lg"
              hasGlow={true}
              onClick={handleStartEvolution}
              className="text-sm md:text-base font-medium"
            >
              {translations.cta}
            </Button>
          </motion.div>
        </motion.div>
      </div>

      {/* Progress indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 h-1 w-3/4 bg-gray-800 rounded-full overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.7 }}
        transition={{ duration: 0.8, delay: 1 }}
      >
        <motion.div
          className="h-full bg-gradient-to-r from-cyan-500 to-purple-600"
          initial={{ width: '0%' }}
          animate={{ width: '20%' }}
          transition={{ duration: 1.5, delay: 1.2 }}
        />
      </motion.div>

      {/* Mind Map Component - Render as portal or outside DOM flow */}
      {isMindMapOpen && (
        <DynamicMindMap
          isOpen={isMindMapOpen}
          onClose={handleCloseMindMap}
          translations={mindMapTranslations}
        />
      )}
    </section>
  );
}
