<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#8B5CF6" stop-opacity="0.1"/>
    </linearGradient>
    <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3B82F6"/>
      <stop offset="100%" stop-color="#8B5CF6"/>
    </linearGradient>
  </defs>
  
  <!-- Background Grid -->
  <g opacity="0.3">
    <!-- Horizontal lines -->
    <line x1="0" y1="100" x2="1200" y2="100" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="0" y1="200" x2="1200" y2="200" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="0" y1="300" x2="1200" y2="300" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="0" y1="400" x2="1200" y2="400" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="0" y1="500" x2="1200" y2="500" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="0" y1="600" x2="1200" y2="600" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="0" y1="700" x2="1200" y2="700" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    
    <!-- Vertical lines -->
    <line x1="100" y1="0" x2="100" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="200" y1="0" x2="200" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="300" y1="0" x2="300" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="400" y1="0" x2="400" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="500" y1="0" x2="500" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="600" y1="0" x2="600" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="700" y1="0" x2="700" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="800" y1="0" x2="800" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="900" y1="0" x2="900" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="1000" y1="0" x2="1000" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
    <line x1="1100" y1="0" x2="1100" y2="800" stroke="#6B7280" stroke-width="0.5" stroke-dasharray="4,4"/>
  </g>
  
  <!-- Network Nodes and Connections -->
  <g>
    <!-- Nodes -->
    <circle cx="200" cy="300" r="8" fill="url(#nodeGradient)"/>
    <circle cx="400" cy="200" r="6" fill="url(#nodeGradient)"/>
    <circle cx="600" cy="400" r="10" fill="url(#nodeGradient)"/>
    <circle cx="800" cy="300" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1000" cy="500" r="9" fill="url(#nodeGradient)"/>
    <circle cx="300" cy="600" r="8" fill="url(#nodeGradient)"/>
    <circle cx="500" cy="700" r="6" fill="url(#nodeGradient)"/>
    <circle cx="700" cy="600" r="10" fill="url(#nodeGradient)"/>
    <circle cx="900" cy="700" r="7" fill="url(#nodeGradient)"/>
    
    <!-- Connections -->
    <line x1="200" y1="300" x2="400" y2="200" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="400" y1="200" x2="600" y2="400" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="600" y1="400" x2="800" y2="300" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="800" y1="300" x2="1000" y2="500" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="200" y1="300" x2="300" y2="600" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="300" y1="600" x2="500" y2="700" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="500" y1="700" x2="700" y2="600" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="700" y1="600" x2="900" y2="700" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="600" y1="400" x2="700" y2="600" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="400" y1="200" x2="300" y2="600" stroke="url(#gridGradient)" stroke-width="1.5"/>
    <line x1="800" y1="300" x2="900" y2="700" stroke="url(#gridGradient)" stroke-width="1.5"/>
  </g>
  
  <!-- Rising Technology Nodes -->
  <g>
    <circle cx="300" cy="500" r="4" fill="#3B82F6" opacity="0.7">
      <animate attributeName="cy" from="500" to="100" dur="10s" repeatCount="indefinite" />
      <animate attributeName="opacity" from="0.7" to="0" dur="10s" repeatCount="indefinite" />
    </circle>
    <circle cx="600" cy="600" r="5" fill="#8B5CF6" opacity="0.8">
      <animate attributeName="cy" from="600" to="200" dur="15s" repeatCount="indefinite" />
      <animate attributeName="opacity" from="0.8" to="0" dur="15s" repeatCount="indefinite" />
    </circle>
    <circle cx="900" cy="550" r="3" fill="#10B981" opacity="0.6">
      <animate attributeName="cy" from="550" to="150" dur="12s" repeatCount="indefinite" />
      <animate attributeName="opacity" from="0.6" to="0" dur="12s" repeatCount="indefinite" />
    </circle>
    <circle cx="450" cy="650" r="4" fill="#F59E0B" opacity="0.7">
      <animate attributeName="cy" from="650" to="250" dur="18s" repeatCount="indefinite" />
      <animate attributeName="opacity" from="0.7" to="0" dur="18s" repeatCount="indefinite" />
    </circle>
    <circle cx="750" cy="700" r="5" fill="#EC4899" opacity="0.8">
      <animate attributeName="cy" from="700" to="300" dur="20s" repeatCount="indefinite" />
      <animate attributeName="opacity" from="0.8" to="0" dur="20s" repeatCount="indefinite" />
    </circle>
  </g>
</svg>
