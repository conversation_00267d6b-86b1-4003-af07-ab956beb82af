import { Exo_2, <PERSON><PERSON><PERSON>, <PERSON> } from 'next/font/google'
import { ReactNode } from 'react'
import { getServerTranslations } from '@/lib/i18n'
import '../../styles/globals.css'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Radial<PERSON>avi<PERSON> from '@/components/layout/RadialNavigation'
import { AnimationContextProvider } from '@/context/AnimationContext'

// Fonts
const exo2 = Exo_2({
  subsets: ['latin'],
  variable: '--font-exo2',
  display: 'swap',
})

const rajdhani = Rajdhani({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-rajdhani',
  display: 'swap',
})

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

export default async function Layout({
  children,
  params,
}: {
  children: ReactNode
  params: { locale: string } | Promise<{ locale: string }>
}) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params
  const { locale } = resolvedParams

  const { t } = await getServerTranslations(locale, ['language'])

  // Pre-translate all texts needed by client components
   const headerTranslations = {
    home: t('navigation.home'),
    services: t('navigation.services'),
    contact: t('navigation.contact'),
    partners: t('navigation.partners'),
    selectLanguage: t('navigation.selectLanguage'),
    logo: {
      alt: t('logo.alt') || 'Elysian Systems Logo',
      fallback: t('logo.fallback') || 'Elysian Systems'
    }
  };

  const radialNavigationTranslations = {
    labs: t('radialNav.labs'),
    architectures: t('radialNav.architectures'),
    futures: t('radialNav.futures'),
    humanPlus: t('radialNav.humanPlus'),
    pulse: t('radialNav.pulse')
  };

  const footerTranslations = {
    footer: {
      elysian: t('footer.elysian'),
      terms: t('footer.terms'),
      copyright: t('footer.copyright'),
      quickLinks: t('footer.quickLinks'),
      contact: t('footer.contact'),
      description: t('footer.description')
    },
    nav: {
      home: t('navigation.home'),
      services: t('navigation.services'),
      partners: t('navigation.partners'),
      contact: t('navigation.contact')
    }
  };

  // Import the default locale from i18n.ts
  const defaultLocale = t('defaultLocale') || locale || 'en';

  return (
    <html lang={locale || defaultLocale}>
      <body
        className={`${exo2.variable} ${rajdhani.variable} ${inter.variable} font-body bg-black text-white overflow-x-hidden`}
        style={{ backgroundColor: '#000000' }}
      >
        <AnimationContextProvider>
          <div className="min-h-screen flex flex-col">
            <Header
              locale={locale}
              translations={headerTranslations}
            />
            <main className="flex-grow">{children}</main>
            <RadialNavigation locale={locale}
            translations={radialNavigationTranslations}
              />
            <Footer
              locale={locale}
              translations={footerTranslations}
            />
          </div>
        </AnimationContextProvider>
      </body>
    </html>
  )
}