// src/components/ui/ServiceCard.tsx
'use client';

import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

type ServiceCardProps = {
  title: string;
  icon?: string;
  lucideIcon?: LucideIcon;
  description: string;
};

export default function ServiceCard({
  title,
  icon,
  lucideIcon: LucideIconComponent,
  description
}: ServiceCardProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    show: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      }
    },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 30px -10px rgba(0, 200, 255, 0.2)",
      transition: {
        duration: 0.2,
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      whileHover="hover"
      className="p-6 rounded-lg border border-gray-800 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm"
    >
      <div className="text-2xl md:text-3xl font-bold mb-4 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center">
        {LucideIconComponent ? (
          <LucideIconComponent className="w-6 h-6 text-white" />
        ) : (
          <span>{icon}</span>
        )}
      </div>
      <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">{title}</h3>
      <p className="text-base font-normal text-gray-300">{description}</p>
    </motion.div>
  );
}