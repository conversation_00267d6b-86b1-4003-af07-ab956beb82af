// src/components/sections/services/CIOSection.tsx
'use client';

import { useEffect } from 'react';
import { motion } from 'framer-motion';
import DataStreams from '@/components/animations/DataStreams';
import { useAnimationContext } from '@/context/AnimationContext';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ServiceCard from '@/components/ui/ServiceCard';

interface CIOSectionProps {
  translations: {
    pageTitle: string;
    pageDescription: string;
    services: Array<{
      title: string;
      icon: string;
      description: string;
    }>;
    approach: {
      title: string;
      description: string;
      pillars: Array<{
        title: string;
        description: string;
      }>;
    };
  };
}

export default function CIOSection({ translations }: CIOSectionProps) {
  const { setActiveSection } = useAnimationContext();
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.2 });

  useEffect(() => {
    if (isIntersecting) {
      setActiveSection('services');
    }
  }, [isIntersecting, setActiveSection]);

  // Use services from translations

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  return (
    <section ref={ref} className="relative w-full pt-6 pb-16 overflow-hidden">
      <div className="absolute inset-0 z-0 opacity-20">
        <DataStreams />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center mb-16">
          {/* Heading is always visible */}
          <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
            {translations.pageTitle}
          </h2>

          {/* Description is always visible */}
          <motion.p
            className="text-gray-300 text-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations.pageDescription}
          </motion.p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="show" // Always show content, regardless of intersection
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8"
        >
          {translations.services.map((service, index) => (
            <ServiceCard
              key={index}
              title={service.title}
              icon={service.icon}
              description={service.description}
            />
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }} // Always show content, regardless of intersection
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 p-6 bg-gray-900/60 backdrop-blur-sm rounded-lg border border-gray-800"
        >
          <h3 className="text-2xl font-bold text-cyan-400 mb-4">{translations.approach.title}</h3>
          <p className="text-gray-300 mb-4">
            {translations.approach.description}
          </p>
          <div className="flex flex-col md:flex-row gap-4 mt-6">
            {translations.approach.pillars.map((pillar, index) => (
              <div key={index} className="flex-1 p-4 bg-black/50 rounded-lg border border-gray-800">
                <h4 className="text-xl font-semibold text-purple-400 mb-2">{pillar.title}</h4>
                <p className="text-gray-400">{pillar.description}</p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}