// src/components/sections/services/MLSection.tsx
'use client';

import { useEffect } from 'react';
import { motion } from 'framer-motion';
import DataStreams from '@/components/animations/DataStreams';
import { useAnimationContext } from '@/context/AnimationContext';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ServiceCard from '@/components/ui/ServiceCard';
import { Cpu } from 'lucide-react';

interface MLSectionProps {
  translations: {
    pageTitle: string;
    pageDescription: string;
    services: Array<{
      title: string;
      icon: string;
      description: string;
    }>;
    mlArchitectureDescription: string;
    process: {
      title: string;
      steps: Array<{
        number: string;
        title: string;
        description: string;
      }>;
    };
  };
}

export default function MLSection({ translations }: MLSectionProps) {
  const { setActiveSection } = useAnimationContext();
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.2 });

  useEffect(() => {
    if (isIntersecting) {
      setActiveSection('services');
    }
  }, [isIntersecting, setActiveSection]);

  // Use services from translations

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  return (
    <section ref={ref} className="relative w-full pt-6 pb-16 overflow-hidden">
      <div className="absolute inset-0 z-0 opacity-20">
        <DataStreams />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-3xl mx-auto text-center mb-16">
          {/* Heading is always visible */}
          <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
            {translations.pageTitle}
          </h2>

          {/* Description is always visible */}
          <motion.p
            className="text-gray-300 text-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations.pageDescription}
          </motion.p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="show" // Always show content, regardless of intersection
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8"
        >
          {translations.services.map((service, index) => {
            if (service.title === 'ML Architecture') {
              return (
                <ServiceCard
                  key={index}
                  title={service.title}
                  lucideIcon={Cpu}
                  description={translations.mlArchitectureDescription}
                />
              );
            }
            return (
              <ServiceCard
                key={index}
                title={service.title}
                icon={service.icon}
                description={service.description}
              />
            );
          })}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }} // Always show content, regardless of intersection
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 bg-gray-900/40 backdrop-blur-sm rounded-lg overflow-hidden"
        >
          <div className="grid md:grid-cols-5 bg-gradient-to-r from-indigo-500/10 to-purple-600/10">
            <div className="md:col-span-3 p-8">
              <h3 className="text-2xl font-bold text-cyan-400 mb-4">{translations.process.title}</h3>
              <ol className="space-y-6 relative before:absolute before:h-full before:w-0.5 before:left-3 before:top-0 before:bg-gradient-to-b before:from-cyan-400 before:to-purple-500 ml-8">
                {translations.process.steps.map((step, index) => {
                  const borderColor = index < 2 ? 'border-cyan-400' : index < 4 ? 'border-purple-400' : 'border-purple-500';
                  return (
                    <li key={index} className="relative pl-8">
                      <span className={`absolute left-[-27px] top-1 h-6 w-6 rounded-full bg-gray-900 border-2 ${borderColor} flex items-center justify-center text-xs`}>
                        {step.number}
                      </span>
                      <h4 className="text-xl font-semibold text-white">{step.title}</h4>
                      <p className="text-gray-300 mt-1">{step.description}</p>
                    </li>
                  );
                })}
              </ol>
            </div>
            <div className="md:col-span-2 bg-black/50 p-8 flex items-center justify-center">
              <div className="w-full h-full max-h-96 flex items-center justify-center">
                {/* This would be a good place for an ML-related animation or graphic */}
                <div className="relative w-64 h-64">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-6xl animate-pulse">🧠</div>
                  </div>
                  <div className="absolute inset-0 animate-spin-slow">
                    <svg viewBox="0 0 100 100" className="w-full h-full fill-cyan-500/20">
                      <circle cx="50" cy="50" r="45" stroke="currentColor" strokeWidth="1" fill="none" />
                      <path d="M50,5 A45,45 0 0,1 95,50" stroke="currentColor" strokeWidth="2" fill="none" />
                    </svg>
                  </div>
                  <div className="absolute inset-0 animate-reverse-spin">
                    <svg viewBox="0 0 100 100" className="w-full h-full fill-purple-500/20">
                      <circle cx="50" cy="50" r="35" stroke="currentColor" strokeWidth="1" fill="none" />
                      <path d="M50,15 A35,35 0 0,1 85,50" stroke="currentColor" strokeWidth="2" fill="none" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}