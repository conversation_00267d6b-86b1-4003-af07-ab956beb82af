'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';

export const LOCALES = ['en', 'sl', 'de', 'hr', 'fr', 'it', 'es', 'zh'] as const;

type LanguageSwitcherProps = {
  currentLocale: string;
  translations: {
    selectLanguage: string;
    languages: Record<string, string>;
  };
}

const languageNames: Record<string, string> = {
  'en': 'English',
  'sl': 'Slovenščina',
  'de': 'Deutsch',
  'hr': 'Hrvatski',
  'fr': 'Français',
  'it': 'Italiano',
  'es': 'Español',
  'zh': '简体中文'
};

const COOKIE_NAME = 'NEXT_LOCALE';

export default function LanguageSwitcher({
  currentLocale,
  translations
}: LanguageSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const switchLanguage = (newLocale: string) => {
    if (newLocale === currentLocale) {
      setIsOpen(false);
      return;
    }

    // Set the cookie to match middleware
    document.cookie = `${COOKIE_NAME}=${newLocale}; path=/; max-age=${60 * 60 * 24 * 365}`;

    // Redirect to the new locale version of the current page
    const segments = pathname.split('/');
    segments[1] = newLocale; // Replace the locale segment
    const newPath = segments.join('/');

    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <div className="relative z-30" style={{ height: '24px', lineHeight: '24px', padding: 0, margin: 0 }}>
      <button
        className="flex items-center gap-2 px-2 py-0.5 rounded-md bg-deep-space border border-electric-blue/30 text-body-small hover:bg-space-black/50 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        style={{ height: '24px', lineHeight: '24px' }}
      >
        <span className="text-white/90">{translations.languages[currentLocale]}</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 text-electric-blue transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              className="fixed inset-0 z-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              className="absolute top-full right-0 mt-2 bg-deep-space border border-electric-blue/30 rounded-md shadow-lg overflow-hidden z-30 min-w-40"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <ul>
                {LOCALES.map((locale) => (
                  <li key={locale}>
                    <button
                      className={`w-full text-left px-4 py-2 text-body-small transition-colors hover:bg-space-black/50 ${
                        locale === currentLocale ? 'text-electric-blue font-medium' : 'text-white/80'
                      }`}
                      onClick={() => switchLanguage(locale)}
                    >
                      {translations.languages[locale]}
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}