{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../server.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,MAAM,CAAA;AACnC,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAA;AAC3B,OAAO,IAAI,MAAM,MAAM,CAAA;AAEvB,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,EAAE,EAAE,CAAC,CAAA;AACrD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAA;AACjD,MAAM,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAA;AACzB,MAAM,MAAM,GAAG,GAAG,CAAC,iBAAiB,EAAE,CAAA;AAEtC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;IACtB,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACxB,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,GAAI,EAAE,IAAI,CAAC,CAAA;QACvC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAA;IAC7B,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAEf,OAAO,CAAC,GAAG,CACT,0CAA0C,IAAI,OAC5C,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,QACpC,EAAE,CACH,CAAA;AACH,CAAC,CAAC,CAAA", "sourcesContent": ["import { createServer } from 'http'\nimport { parse } from 'url'\nimport next from 'next'\n \nconst port = parseInt(process.env.PORT || '3000', 10)\nconst dev = process.env.NODE_ENV !== 'production'\nconst app = next({ dev })\nconst handle = app.getRequestHandler()\n \napp.prepare().then(() => {\n  createServer((req, res) => {\n    const parsedUrl = parse(req.url!, true)\n    handle(req, res, parsedUrl)\n  }).listen(port)\n \n  console.log(\n    `> Server listening at http://localhost:${port} as ${\n      dev ? 'development' : process.env.NODE_ENV\n    }`\n  )\n})"]}