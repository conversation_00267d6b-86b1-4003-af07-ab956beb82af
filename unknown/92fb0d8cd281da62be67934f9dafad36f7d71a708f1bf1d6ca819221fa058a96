import { useEffect, useRef, useState } from 'react';

export default function useIntersectionObserver(options: IntersectionObserverInit) {
  const ref = useRef<HTMLDivElement>(null);
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    // Set default threshold if not provided - use very low threshold (0.01) to detect sections immediately
    const observerOptions = {
      threshold: options.threshold || 0.01,
      rootMargin: options.rootMargin || '0px 0px -10% 0px', // More generous margin for mobile
      root: options.root || null
    };

    const observer = new IntersectionObserver(([entry]) => {
      setEntry(entry);
      setIsIntersecting(entry.isIntersecting);

      // For debugging
      console.log('Element intersection:', entry.isIntersecting, entry.target);
    }, observerOptions);

    const currentRef = ref.current; // Copy ref.current to a local variable
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef); // Use the local variable here
      }
    };
  }, [options]);

  return { ref, entry, isIntersecting };
}