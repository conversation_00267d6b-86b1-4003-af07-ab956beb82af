'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { ZoomIn, ZoomOut, X, Maximize2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import NodeDetailModal from './NodeDetailModal';

interface DynamicMindMapTranslations {
  title: string;
  expandView: string;
  defaultView: string;
  zoomIn: string;
  zoomOut: string;
  close: string;
  keyFeatures: string;
  keyBenefits: string;
  learnMore: string;
  nodes: Array<{
    id: string;
    name: string;
    x: number;
    y: number;
    icon?: string;
    color?: string;
    iconColor?: string;
    navLink?: string;
  }>;
  details: Record<string, {
    title: string;
    description: string;
    icon: string;
    features: string[];
    benefits: string[];
  }>;
  detailNodes: Array<{
    id: string;
    name: string;
    x: number;
    y: number;
    color: string;
    iconColor: string;
  }>;
}

interface DynamicMindMapProps {
  isOpen: boolean;
  onClose: () => void;
  translations: DynamicMindMapTranslations;
}

interface Node {
  id: string;
  name: string;
  x: number;  // Percentage position
  y: number;  // Percentage position
  icon?: string;
  color?: string;
  iconColor?: string; // Custom color for the icon
  navLink?: string;   // Navigation link for the node
}

const DynamicMindMap: React.FC<DynamicMindMapProps> = ({
  isOpen,
  onClose,
  translations
}) => {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [viewMode, setViewMode] = useState<'default' | 'expanded'>('default');
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isInitialAnimation, setIsInitialAnimation] = useState(true);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // Calculate node size based on screen dimensions
  const getNodeSize = useCallback(() => {
    // Default size for larger screens
    let size = 120;

    // Adjust size for smaller screens
    if (dimensions.width < 768) {
      size = 90; // Medium screens
    }
    if (dimensions.width <= 480) {
      size = 70; // Small screens
    }
    if (dimensions.width <= 375) {
      size = 45; // Very small screens
    }

    return {
      width: `${size}px`,
      height: `${size}px`,
      marginLeft: `-${size/2}px`,
      marginTop: `-${size/2}px`
    };
  }, [dimensions.width]);

  // Calculate detail node size based on screen dimensions
  const getDetailNodeSize = useCallback(() => {
    // Default size for larger screens
    let size = 100;

    // Adjust size for smaller screens
    if (dimensions.width < 768) {
      size = 80; // Medium screens
    }
    if (dimensions.width <= 480) {
      size = 60; // Small screens
    }
    if (dimensions.width <= 375) {
      size = 35; // Very small screens
    }

    return {
      width: `${size}px`,
      height: `${size}px`,
      marginLeft: `-${size/2}px`,
      marginTop: `-${size/2}px`
    };
  }, [dimensions.width]);

  // Main service nodes with enhanced styling - positioned in a pentagon layout
  const mainNodes = useMemo(() => translations.nodes, [translations.nodes]);
  // Detailed content for each node
  const detailContent = useMemo(() => translations.details, [translations.details]);

  // Define additional nodes for service details
  const detailNodes = useMemo(() => {
    const map: Record<string, { id: string; name: string; x: number; y: number; color: string; iconColor: string; }> = {};
    translations.detailNodes.forEach(node => { map[node.id] = node; });
    return map;
  }, [translations.detailNodes]);

  // Connection pattern coordinates - matching original pattern with new node positions
  const PATTERN = useMemo(() => ({
    topLeft: { x: 10, y: 26 },
    topRight: { x: 90, y: 26 },
    bottomLeft: { x: 10, y: 74 },
    bottomRight: { x: 90, y: 74 },
    center: { x: 50, y: 50 }
  }), []);

  // Custom connections matching the original pattern from previous commit
  const connections = useMemo(() => [
    // Top edge only
    { customPoints: { x1: PATTERN.topLeft.x, y1: PATTERN.topLeft.y, x2: PATTERN.topRight.x, y2: PATTERN.topRight.y } },

    // Bottom edge only
    { customPoints: { x1: PATTERN.bottomRight.x, y1: PATTERN.bottomRight.y, x2: PATTERN.bottomLeft.x, y2: PATTERN.bottomLeft.y } },

    // Diagonals through the shape
    { customPoints: { x1: PATTERN.topLeft.x, y1: PATTERN.topLeft.y, x2: PATTERN.bottomRight.x, y2: PATTERN.bottomRight.y } },
    { customPoints: { x1: PATTERN.topRight.x, y1: PATTERN.topRight.y, x2: PATTERN.bottomLeft.x, y2: PATTERN.bottomLeft.y } }
  ], [PATTERN]);

  // ML connections - connecting center node to the corners (matching original pattern)
  const mlConnections = useMemo(() => [
    // Connect center node to all corners
    { customPoints: { x1: PATTERN.center.x, y1: PATTERN.center.y, x2: PATTERN.topLeft.x, y2: PATTERN.topLeft.y } },
    { customPoints: { x1: PATTERN.center.x, y1: PATTERN.center.y, x2: PATTERN.topRight.x, y2: PATTERN.topRight.y } },
    { customPoints: { x1: PATTERN.center.x, y1: PATTERN.center.y, x2: PATTERN.bottomRight.x, y2: PATTERN.bottomRight.y } },
    { customPoints: { x1: PATTERN.center.x, y1: PATTERN.center.y, x2: PATTERN.bottomLeft.x, y2: PATTERN.bottomLeft.y } }
  ], [PATTERN]);

  // Service-specific connections
  const serviceConnections = useMemo(() => [
    // Insurance connections
    { source: 'insurance', target: 'insurance-1' },
    { source: 'insurance', target: 'insurance-2' },

    // Technology connections
    { source: 'technology', target: 'technology-1' },
    { source: 'technology', target: 'technology-2' },

    // ML connections
    { source: 'ml', target: 'ml-1' },
    { source: 'ml', target: 'ml-2' },

    // Consulting connections
    { source: 'consulting', target: 'consulting-1' },
    { source: 'consulting', target: 'consulting-2' },

    // Enterprise connections
    { source: 'enterprise', target: 'enterprise-1' },
    { source: 'enterprise', target: 'enterprise-2' }
  ], []);

  // Close detail view function
  const closeDetail = useCallback(() => {
    console.log('Closing detail view');
    setSelectedNode(null);
    setZoomLevel(1);
  }, []);

  // Reset state when component opens
  useEffect(() => {
    if (isOpen) {
      setSelectedNode(null);
      setZoomLevel(1);
      setViewMode('default');
      setIsInitialAnimation(true);

      // Set initial animation to false after entrance animation
      const timer = setTimeout(() => {
        setIsInitialAnimation(false);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Update dimensions when the window resizes
  useEffect(() => {
    if (!isOpen || !containerRef.current) return;

    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, [isOpen]);

  // Handle mouse movement for interactive background
  useEffect(() => {
    if (!isOpen || !containerRef.current) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: ((e.clientX - rect.left) / rect.width) * 100,
          y: ((e.clientY - rect.top) / rect.height) * 100
        });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isOpen]);

  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (selectedNode) {
          // If detail view is open, close it first
          closeDetail();
        } else {
          // Otherwise close the entire mind map
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedNode, onClose, closeDetail]);

  const router = useRouter();

  const handleNodeClick = (nodeId: string) => {
    console.log('Node clicked:', nodeId);
    // If the node is already selected, close it without navigating
    if (selectedNode === nodeId) {
      console.log('Node already selected, closing without navigating');
      setSelectedNode(null);
      setZoomLevel(1);
      return;
    }

    // Set the selected node and zoom
    setSelectedNode(nodeId);
    setZoomLevel(1.5);

    // Get the node to check if it has a navigation link
    const node = getNodeById(nodeId);
    console.log('Node data:', node);

    if (node && node.navLink) {
      console.log('Node has navigation link:', node.navLink);
      // Map node IDs to service section IDs
      const sectionMap: Record<string, string> = {
        'insurance': 'insurance',
        'technology': 'it',
        'ml': 'ml',
        'consulting': 'consulting',
        'enterprise': 'cio'
      };

      // Only set up navigation if a close action hasn't happened
      console.log('Setting up navigation timeout for 2 seconds');
      const navigationTimeout = setTimeout(() => {
        console.log('Navigation timeout triggered');
        // Check if the node is still selected before navigating
        if (selectedNode === nodeId) {
          console.log('Node is still selected, proceeding with navigation');
          const sectionId = sectionMap[nodeId] || '';
          console.log(`Navigating to: ${node.navLink} with section: ${sectionId}`);

          // Store the section ID in localStorage so the services page can select it
          if (sectionId) {
            localStorage.setItem('activeServiceSection', sectionId);
          }

          // Navigate to the specified path with locale
          // Get the current locale from the URL
          const currentLocale = window.location.pathname.split('/')[1];
          console.log(`Current locale: ${currentLocale}`);

          // Construct the path with the current locale
          const path = `/${currentLocale}/${node.navLink}`;
          console.log(`Navigating to: ${path}`);

          router.push(path);
          onClose(); // Close the mind map after navigation
        }
      }, 2000); // Increased delay to allow for better interaction

      // Cleanup timeout if component unmounts or selection changes
      return () => {
        console.log('Cleaning up navigation timeout');
        clearTimeout(navigationTimeout);
      };
    }
  };

  // Toggle expanded view mode
  const toggleViewMode = () => {
    setViewMode(prevMode => prevMode === 'default' ? 'expanded' : 'default');
    if (viewMode === 'default') {
      setZoomLevel(0.8); // Zoom out to see all nodes
    } else {
      setZoomLevel(1); // Reset zoom when returning to default view
    }
  };

  // Get a node by its ID from all available nodes
  const getNodeById = useCallback((id: string): Node | null => {
    // Check main nodes first
    const mainNode = mainNodes.find(n => n.id === id);
    if (mainNode) return mainNode;

    // Check detail nodes
    const detailNode = detailNodes[id as keyof typeof detailNodes];
    if (detailNode) return detailNode;

    return null;
  }, [mainNodes, detailNodes]);

  // Type definition for custom connections
  type CustomConnection = {
    customPoints?: { x1: number, y1: number, x2: number, y2: number };
    source?: string;
    target?: string;
  };

  // Render connections between nodes with enhanced styling
  const renderConnections = useCallback((connectionList: CustomConnection[]) => {
    return connectionList.map((conn, index) => {
      let startX, startY, endX, endY, sourceColor;

      if (conn.customPoints) {
        // Use custom points for original connection pattern
        startX = conn.customPoints.x1;
        startY = conn.customPoints.y1;
        endX = conn.customPoints.x2;
        endY = conn.customPoints.y2;
        sourceColor = '#6366f1'; // Default color for connections
      } else if (conn.source && conn.target) {
        // Use node points for connections between nodes
        const sourceNode = getNodeById(conn.source);
        const targetNode = getNodeById(conn.target);

        if (!sourceNode || !targetNode) return null;

        // Calculate color based on source node or use default
        sourceColor = sourceNode.color || '#6366f1';

        // Use the exact center points of the nodes
        startX = sourceNode.x;
        startY = sourceNode.y;
        endX = targetNode.x;
        endY = targetNode.y;
      } else {
        return null;
      }

      // Calculate animation offset for staggered animation
      const animationDelay = index * 0.2;

      // Create the path for the animated circle
      const motionPath = `M${startX},${startY} L${endX},${endY}`;

      return (
        <g key={`conn-${index}-${conn.source || ''}-${conn.target || ''}`}>
          {/* Main connection line */}
          <line
            x1={`${startX}%`}
            y1={`${startY}%`}
            x2={`${endX}%`}
            y2={`${endY}%`}
            stroke={sourceColor}
            strokeWidth="0.5"
            strokeOpacity="0.7"
            filter="url(#neonGlow)"
            className={isInitialAnimation ? `animate-draw-${index}` : ""}
            style={{
              animationDelay: `${animationDelay}s`,
              animationDuration: "1.5s"
            }}
          />

          {/* Subtle glow overlay */}
          <line
            x1={`${startX}%`}
            y1={`${startY}%`}
            x2={`${endX}%`}
            y2={`${endY}%`}
            stroke="white"
            strokeWidth="0.2"
            strokeOpacity="0.3"
            className={isInitialAnimation ? `animate-draw-${index}` : ""}
            style={{
              animationDelay: `${animationDelay}s`,
              animationDuration: "1.5s"
            }}
          />

          {/* Data flow animation */}
          <circle
            cx={`${startX}%`}
            cy={`${startY}%`}
            r="0.5"
            fill="white"
            fillOpacity="0.9"
            filter="url(#neonGlow)"
            style={{
              animationDuration: `${2 + index % 2}s`
            }}
          >
            <animateMotion
              dur={`${4 + Math.random() * 3}s`}
              repeatCount="indefinite"
              path={motionPath}
            />
          </circle>
        </g>
      );
    });
  }, [getNodeById, isInitialAnimation]);

  if (!isOpen) return null;

  return (
    <motion.div
      className="fixed inset-0 z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div
        className="absolute inset-0 bg-black bg-opacity-95"
        onClick={onClose}
        style={{
          backgroundImage: 'radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.2) 0%, rgba(16, 185, 129, 0.05) 30%, rgba(0, 0, 0, 0.98) 70%)',
          boxShadow: 'inset 0 0 100px rgba(99, 102, 241, 0.2), inset 0 0 50px rgba(236, 72, 153, 0.1)'
        }}
      ></div>

      <div
        ref={containerRef}
        className="relative w-full h-full bg-black bg-opacity-40 overflow-hidden backdrop-filter backdrop-blur-sm"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Enhanced header with title */}
        <motion.div
          className="absolute top-0 left-0 right-0 px-8 py-6 flex items-center justify-between z-20"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <div>
            <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-600">
              {translations.title}
            </h2>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-3">
            <button
              onClick={toggleViewMode}
              className="bg-gray-800 bg-opacity-70 p-2 rounded-full text-indigo-400 hover:text-white hover:bg-indigo-700 transition-all duration-300"
              title={viewMode === 'default' ? translations.expandView : translations.defaultView}
            >
              <Maximize2 size={20} />
            </button>
            <button
              onClick={() => setZoomLevel(prev => Math.min(prev + 0.2, 2))}
              className="bg-gray-800 bg-opacity-70 p-2 rounded-full text-indigo-400 hover:text-white hover:bg-indigo-700 transition-all duration-300"
              title={translations.zoomIn}
            >
              <ZoomIn size={20} />
            </button>
            <button
              onClick={() => setZoomLevel(prev => Math.max(prev - 0.2, 0.5))}
              className="bg-gray-800 bg-opacity-70 p-2 rounded-full text-indigo-400 hover:text-white hover:bg-indigo-700 transition-all duration-300"
              title={translations.zoomOut}
            >
              <ZoomOut size={20} />
            </button>
            <button
              onClick={onClose}
              className="bg-gray-800 bg-opacity-70 p-2 rounded-full text-gray-400 hover:text-white hover:bg-gray-700 transition-all duration-300"
              title={translations.close}
            >
              <X size={20} />
            </button>
          </div>
        </motion.div>

        {/* Mind map container with SVG for connections and nodes */}
        <div
          className="relative w-full h-full flex items-center justify-center"
          style={{
            transform: `scale(${zoomLevel})`,
            transition: 'transform 0.5s ease-out'
          }}
        >
          {/* SVG for connections - using percentage coordinates to match node positions */}
          <svg
            ref={svgRef}
            className="absolute inset-0 w-full h-full"
            preserveAspectRatio="xMidYMid meet"
            viewBox="0 0 100 100" // This makes the SVG use percentages as its coordinate system
          >
            <defs>
              {/* Gradient definitions for connection lines */}
              <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#6366f1" stopOpacity="0.7" />
                <stop offset="100%" stopColor="#10b981" stopOpacity="0.7" />
              </linearGradient>

              {/* Enhanced neon glow filter for nodes */}
              <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
                <feFlood result="flood" floodColor="#6366f1" floodOpacity="0.9"/>
                <feComposite in="flood" result="mask" in2="SourceGraphic" operator="in"/>
                <feGaussianBlur in="mask" result="blurred" stdDeviation="3"/>
                <feComposite in="SourceGraphic" in2="blurred" operator="over"/>
              </filter>

              {/* Cyan neon glow filter */}
              <filter id="cyanGlow" x="-50%" y="-50%" width="200%" height="200%">
                <feFlood result="flood" floodColor="#06b6d4" floodOpacity="0.8"/>
                <feComposite in="flood" result="mask" in2="SourceGraphic" operator="in"/>
                <feGaussianBlur in="mask" result="blurred" stdDeviation="5"/>
                <feComposite in="SourceGraphic" in2="blurred" operator="over"/>
              </filter>

              {/* Pink neon glow filter */}
              <filter id="pinkGlow" x="-50%" y="-50%" width="200%" height="200%">
                <feFlood result="flood" floodColor="#ec4899" floodOpacity="0.8"/>
                <feComposite in="flood" result="mask" in2="SourceGraphic" operator="in"/>
                <feGaussianBlur in="mask" result="blurred" stdDeviation="5"/>
                <feComposite in="SourceGraphic" in2="blurred" operator="over"/>
              </filter>

              {/* Green neon glow filter */}
              <filter id="greenGlow" x="-50%" y="-50%" width="200%" height="200%">
                <feFlood result="flood" floodColor="#10b981" floodOpacity="0.8"/>
                <feComposite in="flood" result="mask" in2="SourceGraphic" operator="in"/>
                <feGaussianBlur in="mask" result="blurred" stdDeviation="5"/>
                <feComposite in="SourceGraphic" in2="blurred" operator="over"/>
              </filter>
            </defs>

            {/* Base connections - original pattern from previous commit */}
            {renderConnections(connections)}

            {/* ML connections - connecting center node to corners */}
            {renderConnections(mlConnections)}

            {/* Service detail connections based on selected node */}
            {selectedNode === 'insurance' && renderConnections(serviceConnections.filter(conn => conn.source === 'insurance'))}
            {selectedNode === 'technology' && renderConnections(serviceConnections.filter(conn => conn.source === 'technology'))}
            {selectedNode === 'ml' && renderConnections(serviceConnections.filter(conn => conn.source === 'ml'))}
            {selectedNode === 'consulting' && renderConnections(serviceConnections.filter(conn => conn.source === 'consulting'))}
            {selectedNode === 'enterprise' && renderConnections(serviceConnections.filter(conn => conn.source === 'enterprise'))}
          </svg>

          {/* Node elements rendered on top of the SVG */}
          {mainNodes.map(node => {
            const isSelected = selectedNode === node.id;

            return (
              <motion.div
                key={node.id}
                initial={isInitialAnimation ? { scale: 0, opacity: 0 } : false}
                animate={{
                  scale: isSelected ? 1.25 : 1,
                  opacity: 1,
                  x: isSelected ? 0 : selectedNode ? 0 : (mousePosition.x - 50) * 0.03, // Disable parallax when any node is selected
                  y: isSelected ? 0 : selectedNode ? 0 : (mousePosition.y - 50) * 0.03
                }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20,
                  delay: isInitialAnimation ? mainNodes.findIndex(n => n.id === node.id) * 0.2 : 0
                }}
                className="absolute cursor-pointer"
                style={{
                  left: `${node.x}%`,
                  top: `${node.y}%`,
                  ...getNodeSize(),
                  zIndex: isSelected ? 30 : 20
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleNodeClick(node.id);
                }}
              >
                {/* Node circle with pulsing effect */}
                <motion.div
                  className="relative flex items-center justify-center w-full h-full"
                  whileHover={{ scale: 1.1 }}
                  style={{
                    borderRadius: '50%',
                    background: `radial-gradient(circle at center, white 5%, ${node.color} 30%, ${node.color}CC 60%, ${node.color}33)`,
                    boxShadow: isSelected
                      ? `0 0 10px white, 0 0 20px ${node.color}, 0 0 40px ${node.color}, 0 0 60px ${node.color}99`
                      : `0 0 5px white, 0 0 15px ${node.color}, 0 0 30px ${node.color}99`,
                    filter: `drop-shadow(0 0 8px ${node.color})`,
                  }}
                >
                  {/* Enhanced neon orbit effect for selected node */}
                  {isSelected && (
                    <div className="absolute inset-0">
                      <div className="w-full h-full rounded-full border-2 border-white border-opacity-40 animate-orbit"
                           style={{ boxShadow: `0 0 10px ${node.color}, 0 0 20px ${node.color}99 inset` }}></div>
                      <div className="w-full h-full rounded-full border border-white border-opacity-20 animate-orbit-reverse"
                           style={{ boxShadow: `0 0 15px ${node.color}99` }}></div>
                    </div>
                  )}

                  <div className="text-center z-10 flex flex-col items-center justify-center h-full">
                    <div
                      className={`${dimensions.width <= 375 ? 'text-base' : dimensions.width <= 480 ? 'text-xl' : 'text-2xl'}`}
                      style={{
                        color: node.iconColor || 'white',
                        textShadow: `0 0 5px ${node.color}, 0 0 10px ${node.color}`,
                        filter: 'brightness(1.2) contrast(1.2)'
                      }}
                    >
                      {node.icon}
                    </div>
                    <div
                      className={`font-bold ${dimensions.width <= 375 ? 'text-[7px] leading-none mt-0.5' : dimensions.width <= 480 ? 'text-[9px] leading-tight' : 'text-xs'} max-w-full px-0.5`}
                      style={{
                        color: 'white',
                        textShadow: '0 0 3px rgba(0, 0, 0, 0.8)'
                      }}
                    >
                      {node.name}
                    </div>
                  </div>
                </motion.div>

                {/* Enhanced neon ripple effect on hover */}
                <div className="absolute inset-0 pointer-events-none">
                  <div
                    className="w-full h-full rounded-full border-2 border-white border-opacity-0 hover:border-opacity-60 transition-all duration-1000"
                    style={{
                      animation: isSelected ? 'ripple 3s infinite ease-out' : undefined,
                      boxShadow: isSelected ? `0 0 15px ${node.color}99, 0 0 30px ${node.color}66` : undefined,
                      filter: `drop-shadow(0 0 5px ${node.color})`
                    }}
                  ></div>
                </div>
              </motion.div>
            );
          })}

          {/* Render detail nodes for each service */}
          {Object.values(detailNodes)
            .filter(node => {
              const prefix = selectedNode + '-';
              return node.id.startsWith(prefix);
            })
            .map(node => (
              <motion.div
                key={node.id}
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="absolute cursor-pointer"
                style={{
                  left: `${node.x}%`,
                  top: `${node.y}%`,
                  ...getDetailNodeSize(),
                  zIndex: 25
                }}
              >
                <div
                  className="w-full h-full rounded-full flex items-center justify-center"
                  style={{
                    background: `radial-gradient(circle at center, white 5%, ${node.color} 30%, ${node.color}99 60%, ${node.color}33)`,
                    boxShadow: `0 0 5px white, 0 0 10px ${node.color}, 0 0 20px ${node.color}99`,
                    filter: `drop-shadow(0 0 5px ${node.color})`
                  }}
                >
                  <div className="text-center flex flex-col items-center justify-center h-full p-2">
                    <div
                      className={`font-bold ${
                        dimensions.width <= 375
                          ? 'text-[6px] leading-none'
                          : dimensions.width <= 480
                            ? 'text-[8px] leading-tight'
                            : 'text-[10px]'
                      } max-w-full px-0.5`}
                      style={{
                        color: 'white',
                        textShadow: `0 0 3px ${node.color}, 0 0 5px rgba(0, 0, 0, 0.8)`
                      }}
                    >
                      {node.name}
                    </div>
                  </div>

                  {/* Enhanced neon ripple effect */}
                  <div className="absolute inset-0 pointer-events-none">
                    <div
                      className="w-full h-full rounded-full border-2 border-white border-opacity-0 hover:border-opacity-60 transition-all duration-1000"
                      style={{
                        animation: 'ripple 3s infinite ease-out',
                        boxShadow: `0 0 15px ${node.color}99, 0 0 30px ${node.color}66`,
                        filter: `drop-shadow(0 0 5px ${node.color})`
                      }}
                    ></div>
                  </div>
                </div>
              </motion.div>
            ))}

        </div>

        {/* Detail popup */}
        <AnimatePresence>
          {selectedNode && (
            <NodeDetailModal
              nodeId={selectedNode}
              onClose={closeDetail}
              nodeData={detailContent[selectedNode as keyof typeof detailContent]}
              navLink={getNodeById(selectedNode)?.navLink}
              translations={{
                keyFeatures: translations.keyFeatures,
                keyBenefits: translations.keyBenefits,
                learnMore: translations.learnMore
              }}
            />
          )}
        </AnimatePresence>

        {/* Enhanced futuristic background effects */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden" aria-hidden="true">
          {/* Enhanced neon grid overlay */}
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: 'linear-gradient(to right, rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(to bottom, rgba(255, 255, 255, 0.3) 1px, transparent 1px)',
              backgroundSize: '40px 40px',
              transform: `translate(${(mousePosition.x - 50) * -0.05}px, ${(mousePosition.y - 50) * -0.05}px)`,
              transition: 'transform 0.3s ease-out',
              filter: 'drop-shadow(0 0 2px #6366f1) drop-shadow(0 0 5px rgba(99, 102, 241, 0.5))'
            }}
          />

          {/* Enhanced neon particle effects */}
          <div className="absolute inset-0">
            {[...Array(60)].map((_, i) => {
              // Randomly select a neon color
              const colors = ['#6366f1', '#ec4899', '#06b6d4', '#10b981', '#ffffff'];
              const color = colors[Math.floor(Math.random() * colors.length)];

              return (
                <motion.div
                  key={`particle-${i}`}
                  className="absolute"
                  initial={{
                    width: `${Math.random() * 2 + 1}px`,
                    height: `${Math.random() * 80 + 20}px`,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    opacity: Math.random() * 0.4 + 0.1,
                    rotate: Math.random() * 360,
                    background: color,
                    boxShadow: `0 0 5px ${color}, 0 0 10px ${color}`,
                    filter: `blur(${Math.random() * 1}px)`
                  }}
                  animate={{
                    opacity: [0.1, 0.4, 0.1],
                    scale: [1, 1.2, 1],
                    filter: [`blur(${Math.random() * 1}px)`, `blur(${Math.random() * 2 + 1}px)`, `blur(${Math.random() * 1}px)`],
                    rotate: [`${Math.random() * 360}deg`, `${Math.random() * 360 + 180}deg`]
                  }}
                  transition={{
                    duration: Math.random() * 10 + 15,
                    ease: "linear",
                    repeat: Infinity,
                    repeatType: "loop"
                  }}
                />
              );
            })}
          </div>
        </div>

        {/* Enhanced neon animations */}
        <style jsx>{`
          @keyframes ripple {
            0% {
              transform: scale(1);
              opacity: 1;
              box-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(99, 102, 241, 0.6);
            }
            50% {
              box-shadow: 0 0 25px rgba(255, 255, 255, 0.5), 0 0 40px rgba(99, 102, 241, 0.4);
            }
            100% {
              transform: scale(1.8);
              opacity: 0;
              box-shadow: 0 0 15px rgba(255, 255, 255, 0), 0 0 20px rgba(99, 102, 241, 0);
            }
          }

          @keyframes orbit {
            0% {
              transform: rotate(0deg);
              box-shadow: 0 0 15px rgba(99, 102, 241, 0.6);
            }
            50% {
              box-shadow: 0 0 25px rgba(99, 102, 241, 0.8);
            }
            100% {
              transform: rotate(360deg);
              box-shadow: 0 0 15px rgba(99, 102, 241, 0.6);
            }
          }

          @keyframes orbit-reverse {
            0% {
              transform: rotate(0deg);
              box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            }
            50% {
              box-shadow: 0 0 20px rgba(255, 255, 255, 0.7);
            }
            100% {
              transform: rotate(-360deg);
              box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            }
          }

          @keyframes float {
            0% {
              transform: translateY(0);
              filter: brightness(1);
            }
            50% {
              transform: translateY(-20px);
              filter: brightness(1.2);
            }
            100% {
              transform: translateY(0);
              filter: brightness(1);
            }
          }

          @keyframes draw-line {
            0% {
              stroke-dashoffset: 100;
              stroke-opacity: 0.1;
              filter: blur(1px);
            }
            50% {
              filter: blur(0.5px);
            }
            100% {
              stroke-dashoffset: 0;
              stroke-opacity: 1;
              filter: blur(0);
            }
          }

          @keyframes pulse-particle {
            0% {
              opacity: 0;
              transform: scale(0.8);
              filter: brightness(0.8);
            }
            50% {
              opacity: 1;
              transform: scale(1.2);
              filter: brightness(1.5);
            }
            100% {
              opacity: 0;
              transform: scale(0.8);
              filter: brightness(0.8);
            }
          }
        `}</style>
      </div>
    </motion.div>
  );
};

export default DynamicMindMap;
