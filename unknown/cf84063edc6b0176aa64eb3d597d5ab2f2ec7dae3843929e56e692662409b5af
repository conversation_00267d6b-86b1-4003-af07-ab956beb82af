import PartnersSection from '@/components/sections/PartnersSection';
import { getServerTranslations } from '@/lib/i18n';

export default async function PartnersPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const partnersTranslations = {
    title: t('partners.title'),
    subtitle: t('partners.subtitle'),
    becomePartnerTitle: t('partners.becomePartnerTitle'),
    becomePartnerText: t('partners.becomePartnerText'),
    contactButton: t('partners.contactButton'),
    visitWebsite: t('partners.visitWebsite')
  };

  return <PartnersSection translations={partnersTranslations} />;
}