// src/app/[locale]/services/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import ServicesPage from '@/components/sections/services/ServicesPage';

// Define types for translation objects
interface ServiceTranslation {
  title: string;
  icon: string;
  description: string;
}
interface PillarTranslation {
  title: string;
  description: string;
}
interface StepTranslation {
  number: number;
  title: string;
  description: string;
}
interface PhaseTranslation {
  title: string;
  description: string;
}

export default async function ServicesPageWrapper({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const servicesTranslations = {
    title: t('services.title'),
    pageTitle: t('services.pageTitle'),
    pageSubtitle: t('services.pageSubtitle'),
    sections: [
      { id: 'insurance', title: t('services.insuranceTech.title') },
      { id: 'it', title: t('services.itConsulting.title') },
      { id: 'ml', title: t('services.machineLearning.title') },
      { id: 'consulting', title: t('services.enterpriseArchitecture.title') },
      { id: 'cio', title: t('services.cioExperienceHub.title') },
    ],
    insurance: {
      pageTitle: t('services.insuranceTech.pageTitle'),
      pageDescription: t('services.insuranceTech.pageDescription'),
      services: (() => {
        const services = t('services.insuranceTech.services');
        return Array.isArray(services)
          ? services.map((service: ServiceTranslation) => ({
              title: service.title,
              icon: service.icon,
              description: service.description
            }))
          : [];
      })(),
      philosophy: {
        title: t('services.insuranceTech.philosophy.title'),
        description: t('services.insuranceTech.philosophy.description'),
        pillars: (() => {
          const pillars = t('services.insuranceTech.philosophy.pillars');
          return Array.isArray(pillars)
            ? pillars.map((pillar: PillarTranslation) => ({
                title: pillar.title,
                description: pillar.description
              }))
            : [];
        })()
      }
    },
    ml: {
      pageTitle: t('services.machineLearning.pageTitle'),
      pageDescription: t('services.machineLearning.pageDescription'),
      services: (() => {
        const services = t('services.machineLearning.services');
        return Array.isArray(services)
          ? services.map((service: ServiceTranslation) => ({
              title: service.title,
              icon: service.icon,
              description: service.description
            }))
          : [];
      })(),
      mlArchitectureDescription: t('services.machineLearning.mlArchitectureDescription'),
      process: {
        title: t('services.machineLearning.process.title'),
        steps: (() => {
          const steps = t('services.machineLearning.process.steps');
          return Array.isArray(steps)
            ? steps.map((step: StepTranslation) => ({
                number: String(step.number),
                title: step.title,
                description: step.description
              }))
            : [];
        })()
      }
    },
    it: {
      pageTitle: t('services.itConsulting.pageTitle'),
      pageDescription: t('services.itConsulting.pageDescription'),
      services: (() => {
        const services = t('services.itConsulting.services');
        return Array.isArray(services)
          ? services.map((service: ServiceTranslation) => ({
              title: service.title,
              icon: service.icon,
              description: service.description
            }))
          : [];
      })(),
      philosophy: {
        title: t('services.itConsulting.philosophy.title'),
        description: t('services.itConsulting.philosophy.description'),
        pillars: (() => {
          const pillars = t('services.itConsulting.philosophy.pillars');
          return Array.isArray(pillars)
            ? pillars.map((pillar: PillarTranslation) => ({
                title: pillar.title,
                description: pillar.description
              }))
            : [];
        })()
      }
    },
    consulting: {
      pageTitle: t('services.enterpriseArchitecture.pageTitle'),
      pageDescription: t('services.enterpriseArchitecture.pageDescription'),
      services: (() => {
        const services = t('services.enterpriseArchitecture.services');
        return Array.isArray(services)
          ? services.map((service: ServiceTranslation) => ({
              title: service.title,
              icon: service.icon,
              description: service.description
            }))
          : [];
      })(),
      methodology: {
        title: t('services.enterpriseArchitecture.methodology.title'),
        description: t('services.enterpriseArchitecture.methodology.description'),
        phases: (() => {
          const phases = t('services.enterpriseArchitecture.methodology.phases');
          return Array.isArray(phases)
            ? phases.map((phase: PhaseTranslation) => ({
                title: phase.title,
                description: phase.description
              }))
            : [];
        })()
      }
    },
    cio: {
      pageTitle: t('services.cioExperienceHub.pageTitle'),
      pageDescription: t('services.cioExperienceHub.pageDescription'),
      services: (() => {
        const services = t('services.cioExperienceHub.services');
        return Array.isArray(services)
          ? services.map((service: ServiceTranslation) => ({
              title: service.title,
              icon: service.icon,
              description: service.description
            }))
          : [];
      })(),
      approach: {
        title: t('services.cioExperienceHub.approach.title'),
        description: t('services.cioExperienceHub.approach.description'),
        pillars: (() => {
          const pillars = t('services.cioExperienceHub.approach.pillars');
          return Array.isArray(pillars)
            ? pillars.map((pillar: PillarTranslation) => ({
                title: pillar.title,
                description: pillar.description
              }))
            : [];
        })()
      }
    }
  };

  return <ServicesPage translations={servicesTranslations} />;
}