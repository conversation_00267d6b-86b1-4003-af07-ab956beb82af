'use client'

import Image from 'next/image'

type LogoProps = {
  width?: number;
  className?: string;
  translations: {
    alt: string;
    fallback: string;
  };
}

export default function ElysianLogo({ width = 150, className = '', translations }: LogoProps) {
  // Use a fixed aspect ratio for the logo
  const aspectRatio = 0.75; // 4:3 aspect ratio (width:height)
  const height = Math.round(width / aspectRatio);

  // Create a specified width with correct aspect ratio
  return (
    <div className={`inline-block ${className}`} style={{
      width: `${width}px`,
      height: `${height}px`,
      position: 'relative'
    }}>
      <Image
        src="/images/elysian-logo.png"
        alt={translations.alt}
        fill
        sizes={`${width}px`}
        priority
        style={{ objectFit: 'contain' }}
      />

      {/* Fallback if image doesn't load */}
      <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100">
        <div className="relative">
          <span className="font-geometric font-bold text-xl bg-clip-text text-transparent bg-gradient-to-r from-electric-blue to-cyber-magenta">
            {translations.fallback}
          </span>
          <div className="absolute -inset-1 -z-10 rounded-full bg-gradient-to-r from-yellow-300 via-electric-blue to-cyber-magenta opacity-0 blur-sm"></div>
        </div>
      </div>
    </div>
  )
}
