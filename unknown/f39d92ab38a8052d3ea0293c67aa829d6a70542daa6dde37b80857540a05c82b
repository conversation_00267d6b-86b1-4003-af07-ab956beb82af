// src/components/layout/Footer.tsx
'use client'

import Link from 'next/link'

type FooterProps = {
  locale: string;
  translations: {
    footer: {
      elysian: string;
      description: string;
      copyright: string; // made required
    };
    nav: {
      home: string;
      services: string;
      partners: string;
      contact: string;
    };
  };
};

export default function Footer({ locale, translations }: FooterProps) {
  return (
    <footer className="bg-neutral-950 border-t border-white/10 px-6 py-6">
      <div className="max-w-3xl mx-auto flex flex-col items-center text-center">
        {/* Logo and Tagline */}
        <Link href={`/${locale}`} className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-500 font-geometric tracking-tight">
          {translations.footer.elysian}
        </Link>
        <p className="mt-2 text-white/70 text-sm max-w-xl">
          {translations.footer.description}
        </p>

        {/* Copyright and LinkedIn */}
        <div className="mt-4 pt-4 border-t border-white/10 w-full flex flex-row items-center justify-between">
          <p className="text-white/50 text-xs">
            © {new Date().getFullYear()} {translations.footer.copyright}
          </p>

          <a
            href="https://www.linkedin.com/company/elysian-systems-ltd"
            target="_blank"
            rel="noopener noreferrer"
            className="w-7 h-7 rounded-full bg-[#0077b5] hover:opacity-90 flex items-center justify-center text-white transition-all hover:scale-105"
            aria-label="LinkedIn"
          >
            <span className="sr-only">LinkedIn</span>
            <svg className="w-3.5 h-3.5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
            </svg>
          </a>
        </div>
      </div>
    </footer>
  )
}