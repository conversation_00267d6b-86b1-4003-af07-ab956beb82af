{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "sourceMap": true, "inlineSources": true, "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": "./", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@sections/*": ["src/components/sections/*"], "@ui/*": ["src/components/ui/*"], "@animations/*": ["src/components/animations/*"], "@hooks/*": ["src/hooks/*"], "@context/*": ["src/context/*"], "@lib/*": ["src/lib/*"], "@styles/*": ["src/styles/*"], "@types/*": ["src/types/*"], "@public/*": ["public/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}