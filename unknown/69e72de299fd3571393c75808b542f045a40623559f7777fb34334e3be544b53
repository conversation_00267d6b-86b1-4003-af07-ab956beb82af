<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="512" height="512" rx="64" fill="#050510"/>

  <!-- Stylized "E" for Elysian -->
  <path d="M128 128h256v64H192v64h192v64H192v64h256v64H128V128z" fill="url(#gradient)"/>

  <!-- Neural network nodes -->
  <circle cx="160" cy="160" r="16" fill="#00E5FF" filter="url(#glow)"/>
  <circle cx="352" cy="160" r="16" fill="#00E5FF" filter="url(#glow)"/>
  <circle cx="256" cy="256" r="16" fill="#00E5FF" filter="url(#glow)"/>
  <circle cx="160" cy="352" r="16" fill="#00E5FF" filter="url(#glow)"/>
  <circle cx="352" cy="352" r="16" fill="#00E5FF" filter="url(#glow)"/>

  <!-- Neural connections -->
  <line x1="160" y1="160" x2="256" y2="256" stroke="#7B00FF" stroke-width="4"/>
  <line x1="352" y1="160" x2="256" y2="256" stroke="#7B00FF" stroke-width="4"/>
  <line x1="160" y1="352" x2="256" y2="256" stroke="#7B00FF" stroke-width="4"/>
  <line x1="352" y1="352" x2="256" y2="256" stroke="#7B00FF" stroke-width="4"/>

  <!-- Gradient and glow definitions -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00FFFF"/>
      <stop offset="100%" stop-color="#FF00FF"/>
    </linearGradient>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>
</svg>
