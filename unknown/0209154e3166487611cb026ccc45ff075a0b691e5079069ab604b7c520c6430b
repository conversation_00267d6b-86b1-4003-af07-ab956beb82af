// src/lib/seo.ts

export type MetadataProps = {
    title?: string;
    description?: string;
    keywords?: string[];
    image?: string;
    locale?: string;
    type?: string;
    noIndex?: boolean;
    canonicalUrl?: string;
  }
  
  const siteConfig = {
    name: 'Elysian Systems',
    url: 'https://elysian-systems.com',
    defaultDescription: 'Enterprise transformation through neural network-inspired solutions.',
    defaultImage: '/images/og-image.png',
    defaultKeywords: ['neural network', 'enterprise', 'digital transformation', 'AI', 'machine learning'],
    twitterHandle: '@neuralenterprise',
  }
  
  export const getSEOMetadata = ({
    title,
    description = siteConfig.defaultDescription,
    keywords = siteConfig.defaultKeywords,
    image = siteConfig.defaultImage,
    locale = 'en',
    type = 'website',
    noIndex = false,
    canonicalUrl,
  }: MetadataProps = {}) => {
    const pageTitle = title 
      ? `${title} | ${siteConfig.name}`
      : siteConfig.name;
      
    const imageUrl = image.startsWith('http') 
      ? image
      : `${siteConfig.url}${image}`;
      
    const canonical = canonicalUrl || siteConfig.url;
  
    return {
      title: pageTitle,
      description,
      keywords: keywords.join(', '),
      openGraph: {
        title: pageTitle,
        description,
        url: canonical,
        siteName: siteConfig.name,
        images: [{
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: pageTitle,
        }],
        locale,
        type,
      },
      twitter: {
        card: 'summary_large_image',
        title: pageTitle,
        description,
        images: [imageUrl],
        creator: siteConfig.twitterHandle,
      },
      alternates: {
        canonical: canonical,
      },
      robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    };
  };
  
  export const getLocalizedMetadata = (
    baseMetadata: MetadataProps, 
    locale: string
  ): MetadataProps => {
    // Generate the proper alternate links
    return {
      ...baseMetadata,
      locale,
      canonicalUrl: `${siteConfig.url}/${locale}`,
    };
  };