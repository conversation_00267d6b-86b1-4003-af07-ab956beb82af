// middleware.ts
import { NextRequest, NextResponse } from 'next/server';

// Define your locales - using the same ones from your original config
const locales = ['en', 'sl', 'de', 'hr', 'fr', 'it', 'es', 'zh'];
const defaultLocale = 'en';
console.log('Available locales:', locales);
console.log('Default locale:', defaultLocale);

// Function to get locale from URL pathname
function getLocaleFromPathname(pathname: string): string | undefined {
  const segments = pathname.split('/');
  console.log('Path segments:', segments);
  // The first segment after the leading slash
  const firstSegment = segments[1];
  console.log('Path name:', pathname);
  console.log('First segment:', firstSegment);
  if (locales.includes(firstSegment)) {
    return firstSegment;
  }

  return undefined;
}

// Get the preferred locale from cookie, header, or default
function getPreferredLocale(request: NextRequest): string {
  // Check for locale in cookie first (user preference)
  const cookieLocale = request.cookies.get('NEXT_LOCALE')?.value;
  if (cookieLocale && locales.includes(cookieLocale)) {
    console.log('Locale from cookie:', cookieLocale);
    return cookieLocale;
  }

  // Check for locale in Accept-Language header
  const acceptLanguage = request.headers.get('Accept-Language');
  if (acceptLanguage) {
    // Parse the Accept-Language header to get the preferred languages
    console.log('Accept-Language header:', acceptLanguage);
    const preferredLocales = acceptLanguage
      .split(',')
      .map(part => {
        const [locale, priority] = part.trim().split(';q=');
        return {
          locale: locale.split('-')[0], // Get the language part only
          priority: priority ? parseFloat(priority) : 1.0
        };
      })
      .sort((a, b) => b.priority - a.priority);

    // Find the first supported locale
    for (const { locale } of preferredLocales) {
      if (locales.includes(locale)) {
        console.log('first supported locale:', locale);
        return locale;
      }
    }
  }

  // Default to defaultLocale if no match is found
  return defaultLocale;
}

export function middleware(request: NextRequest) {
  // Get pathname from the URL
  const { pathname } = request.nextUrl;
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/en', request.url))
  }
  // Skip handling for assets, API routes, etc.
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    /\.[a-z]+$/i.test(pathname) // Files with extensions (images, etc.)
  ) {
    return NextResponse.next();
  }

  // Check if pathname already has a locale
  const pathnameLocale = getLocaleFromPathname(pathname);

  // If there's already a locale in the pathname, no need to redirect
  if (pathnameLocale) {
    // Store the locale in a cookie for future requests
    const response = NextResponse.next();
    response.cookies.set('NEXT_LOCALE', pathnameLocale);
    return response;
  }

  // If no locale in pathname, redirect to the correct locale
  const preferredLocale = getPreferredLocale(request);
  const newUrl = new URL(`/${preferredLocale}${pathname === '/' ? '' : pathname}`, request.url);

  // Copy the search params
  newUrl.search = request.nextUrl.search;

  // Create a response that redirects to the localized URL and sets a cookie
  const response = NextResponse.redirect(newUrl);
  response.cookies.set('NEXT_LOCALE', preferredLocale);

  return response;
}

export const config = {
  // Define on which paths this middleware will run
  matcher: [
    // Skip all internal paths (_next), API routes, static files, and files with extensions
    '/((?!_next|api|static|.*\\..*).*)'
  ],
};