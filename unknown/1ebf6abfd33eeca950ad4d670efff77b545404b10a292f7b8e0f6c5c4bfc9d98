// next.config.turbo.ts
import { NextConfig } from 'next';

const nextConfig: NextConfig = {
  reactStrictMode: true,
  // Generate source maps for client-side and server-side code in production
  productionBrowserSourceMaps: true,

  // Simplified configuration for Next.js 15.3.1
  // Only include essential external packages
  serverExternalPackages: [
    'nodemailer'
  ],

  experimental: {
    // Enable server actions with appropriate settings
    serverActions: {
      bodySizeLimit: '1mb',
      allowedOrigins: ['*']
    },
    // Enable source maps for server-side code in development
    serverSourceMaps: true,
    // Disable server minification to improve debugging
    serverMinification: false,
  },

  // Configure Turbopack only
  turbopack: {
    // Add your rules here if needed
    rules: {
      // Example: "*.css": ["style-loader", "css-loader"]
    },
  },

  // No webpack configuration here
};

export default nextConfig;