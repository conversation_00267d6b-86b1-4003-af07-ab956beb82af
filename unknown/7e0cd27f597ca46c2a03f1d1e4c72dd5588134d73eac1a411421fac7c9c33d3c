'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useAnimationContext } from '@/context/AnimationContext';

interface AnimatedBackgroundProps {
  type?: 'neural' | 'particles' | 'grid' | 'hexagons';
  density?: 'low' | 'medium' | 'high';
  color?: string;
  secondaryColor?: string;
  speed?: 'slow' | 'medium' | 'fast';
  interactive?: boolean;
  className?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  type = 'neural',
  density = 'medium',
  color = '#00f2fe',
  secondaryColor = '#4facfe',
  speed = 'medium',
  interactive = true,
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const { activeSection } = useAnimationContext();
  const animationFrameRef = useRef<number>(0);
  const lastTimeRef = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;

    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match container
    const resizeCanvas = () => {
      const { offsetWidth, offsetHeight } = container;
      canvas.width = offsetWidth;
      canvas.height = offsetHeight;
      setDimensions({ width: offsetWidth, height: offsetHeight });
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Determine the number of elements based on density
    const densityMap = {
      low: 50,
      medium: 100,
      high: 200
    };

    const elementCount = densityMap[density];

    // Set animation speed based on speed prop
    const speedMap = {
      slow: 0.5,
      medium: 1,
      fast: 2
    };

    const animationSpeed = speedMap[speed];

    // Parse colors
    const primaryColor = color;
    const secondary = secondaryColor;

    // Mouse tracking for interactive mode
    const mouse = { 
      x: canvas.width / 2, 
      y: canvas.height / 2,
      vx: 0,
      vy: 0,
      lastX: canvas.width / 2,
      lastY: canvas.height / 2
    };

    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouse.lastX = mouse.x;
      mouse.lastY = mouse.y;
      mouse.x = e.clientX - rect.left;
      mouse.y = e.clientY - rect.top;
      mouse.vx = mouse.x - mouse.lastX;
      mouse.vy = mouse.y - mouse.lastY;
    };

    const handleMouseLeave = () => {
      mouse.vx = 0;
      mouse.vy = 0;
      mouse.x = canvas.width / 2;
      mouse.y = canvas.height / 2;
    };

    if (interactive && container) {
      container.addEventListener('mousemove', handleMouseMove);
      container.addEventListener('mouseleave', handleMouseLeave);
    }

    interface Neuron {
      x: number;
      y: number;
      size: number;
      connections: number[];
      pulses: { progress: number, target: number }[];
      speedX?: number;
      speedY?: number;
      color: string;
      opacity: number;
    }

    interface Particle {
      x: number;
      y: number;
      size: number;
      speedX?: number;
      speedY?: number;
      color: string;
      originalX?: number;
      originalY?: number;
      radius?: number;
      opacity?: number;
    }

    // Initialize elements based on type
    const elements: (Particle | Neuron)[] = [];

    // Neural network specific elements
    const neurons: Neuron[] = [];
    const maxConnections = 5;

    if (type === 'neural') {
      // Create neurons
      for (let i = 0; i < elementCount * 0.3; i++) {
        neurons.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 4 + 2,
          connections: [],
          pulses: [],
          speedX: (Math.random() - 0.5) * animationSpeed * 0.2,
          speedY: (Math.random() - 0.5) * animationSpeed * 0.2,
          color: Math.random() > 0.3 ? primaryColor : secondary,
          opacity: Math.random() * 0.5 + 0.5
        });
      }

      // Create connections between neurons
      for (let i = 0; i < neurons.length; i++) {
        const connectionCount = Math.floor(Math.random() * maxConnections) + 1;
        const possibleTargets = [...Array(neurons.length).keys()].filter(idx => idx !== i);
        
        for (let j = 0; j < connectionCount; j++) {
          if (possibleTargets.length === 0) break;
          
          const randomIndex = Math.floor(Math.random() * possibleTargets.length);
          const targetIndex = possibleTargets[randomIndex];
          
          neurons[i].connections.push(targetIndex);
          possibleTargets.splice(randomIndex, 1);
        }
      }
    } else if (type === 'particles') {
      for (let i = 0; i < elementCount; i++) {
        elements.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 3 + 1,
          speedX: (Math.random() - 0.5) * animationSpeed,
          speedY: (Math.random() - 0.5) * animationSpeed,
          color: Math.random() > 0.5 ? primaryColor : secondary,
          opacity: Math.random() * 0.5 + 0.5
        });
      }
    } else if (type === 'grid') {
      const gridSize = Math.floor(Math.sqrt(elementCount));
      const cellWidth = canvas.width / gridSize;
      const cellHeight = canvas.height / gridSize;

      for (let i = 0; i < gridSize; i++) {
        for (let j = 0; j < gridSize; j++) {
          elements.push({
            x: i * cellWidth + cellWidth / 2,
            y: j * cellHeight + cellHeight / 2,
            size: 1,
            originalX: i * cellWidth + cellWidth / 2,
            originalY: j * cellHeight + cellHeight / 2,
            color: Math.random() > 0.7 ? primaryColor : secondary,
            opacity: Math.random() * 0.5 + 0.5
          });
        }
      }
    } else if (type === 'hexagons') {
      const hexRadius = 15;
      const horizontalSpacing = hexRadius * Math.sqrt(3);
      const verticalSpacing = hexRadius * 1.5;

      const columns = Math.ceil(canvas.width / horizontalSpacing) + 1;
      const rows = Math.ceil(canvas.height / verticalSpacing) + 1;

      for (let i = 0; i < columns; i++) {
        for (let j = 0; j < rows; j++) {
          const x = i * horizontalSpacing + (j % 2 === 0 ? 0 : horizontalSpacing / 2);
          const y = j * verticalSpacing;

          elements.push({
            x,
            y,
            originalX: x,
            originalY: y,
            radius: hexRadius,
            size: hexRadius,
            opacity: Math.random() * 0.5 + 0.1,
            color: Math.random() > 0.7 ? primaryColor : secondary,
          });
        }
      }
    }

    // Create pulses in neural network
    const createPulse = (neuronIndex: number, targetIndex: number) => {
      if (Math.random() > 0.998) {
        neurons[neuronIndex].pulses.push({
          progress: 0,
          target: targetIndex
        });
      }
    };

    // Draw hexagon
    const drawHexagon = (x: number, y: number, size: number) => {
      const numberOfSides = 6;
      const step = 2 * Math.PI / numberOfSides;
      
      ctx.beginPath();
      for (let i = 0; i < numberOfSides; i++) {
        const curStep = i * step + Math.PI / 6;
        if (i === 0) {
          ctx.moveTo(x + size * Math.cos(curStep), y + size * Math.sin(curStep));
        } else {
          ctx.lineTo(x + size * Math.cos(curStep), y + size * Math.sin(curStep));
        }
      }
      ctx.closePath();
    };

    // Animate neural network with data flowing through connections
    const animateNeural = (timestamp: number) => {
      const deltaTime = timestamp - (lastTimeRef.current || timestamp);
      lastTimeRef.current = timestamp;
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Update neurons
      neurons.forEach((neuron, idx) => {
        // Update position with subtle movement
        neuron.x += (neuron.speedX || 0);
        neuron.y += (neuron.speedY || 0);
        
        // Add some mouse influence
        if (interactive) {
          const dx = mouse.x - neuron.x;
          const dy = mouse.y - neuron.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 150) {
            const force = 0.02 * (150 - distance) / 150;
            neuron.x -= dx * force;
            neuron.y -= dy * force;
          }
        }
        
        // Bounce off edges
        if (neuron.x < 0 || neuron.x > canvas.width) neuron.speedX = -(neuron.speedX || 0);
        if (neuron.y < 0 || neuron.y > canvas.height) neuron.speedY = -(neuron.speedY || 0);
        
        // Create new pulses
        neuron.connections.forEach(targetIdx => {
          createPulse(idx, targetIdx);
        });
        
        // Update pulses
        neuron.pulses = neuron.pulses.filter(pulse => {
          pulse.progress += 0.01 * animationSpeed;
          return pulse.progress <= 1;
        });
      });
      
      // Draw connections
      ctx.strokeStyle = primaryColor;
      ctx.lineWidth = 0.5;
      
      neurons.forEach((neuron, idx) => {
        neuron.connections.forEach(targetIdx => {
          const target = neurons[targetIdx];
          
          ctx.beginPath();
          ctx.moveTo(neuron.x, neuron.y);
          ctx.lineTo(target.x, target.y);
          ctx.globalAlpha = 0.2;
          ctx.stroke();
          ctx.globalAlpha = 1;
        });
      });
      
      // Draw pulses
      ctx.fillStyle = secondaryColor;
      
      neurons.forEach(neuron => {
        neuron.pulses.forEach(pulse => {
          const target = neurons[pulse.target];
          
          const x = neuron.x + (target.x - neuron.x) * pulse.progress;
          const y = neuron.y + (target.y - neuron.y) * pulse.progress;
          
          ctx.beginPath();
          ctx.arc(x, y, 2, 0, Math.PI * 2);
          ctx.globalAlpha = 1 - Math.abs(pulse.progress - 0.5) * 2;
          ctx.fill();
          ctx.globalAlpha = 1;
        });
      });
      
      // Draw neurons
      neurons.forEach(neuron => {
        // Draw nucleus
        ctx.beginPath();
        ctx.arc(neuron.x, neuron.y, neuron.size, 0, Math.PI * 2);
        ctx.fillStyle = neuron.color;
        ctx.globalAlpha = neuron.opacity;
        ctx.fill();
        
        // Draw glow
        const gradient = ctx.createRadialGradient(
          neuron.x, neuron.y, neuron.size,
          neuron.x, neuron.y, neuron.size * 3
        );
        gradient.addColorStop(0, neuron.color);
        gradient.addColorStop(1, 'rgba(0,0,0,0)');
        
        ctx.beginPath();
        ctx.arc(neuron.x, neuron.y, neuron.size * 3, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.globalAlpha = 0.3;
        ctx.fill();
        ctx.globalAlpha = 1;
      });
      
      // Mouse cursor effect
      if (interactive && (mouse.vx !== 0 || mouse.vy !== 0)) {
        ctx.beginPath();
        ctx.arc(mouse.x, mouse.y, 30, 0, Math.PI * 2);
        const gradient = ctx.createRadialGradient(
          mouse.x, mouse.y, 0,
          mouse.x, mouse.y, 30
        );
        gradient.addColorStop(0, 'rgba(255,255,255,0.2)');
        gradient.addColorStop(1, 'rgba(255,255,255,0)');
        ctx.fillStyle = gradient;
        ctx.fill();
      }
      
      animationFrameRef.current = requestAnimationFrame(animateNeural);
    };

    // Animation loop for other types
    const animate = (timestamp: number) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (type === 'particles') {
        // Draw connection lines between nearby particles
        ctx.strokeStyle = primaryColor;
        ctx.lineWidth = 0.3;

        for (let i = 0; i < elements.length; i++) {
          for (let j = i + 1; j < elements.length; j++) {
            const dx = elements[i].x - elements[j].x;
            const dy = elements[i].y - elements[j].y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
              ctx.beginPath();
              ctx.moveTo(elements[i].x, elements[i].y);
              ctx.lineTo(elements[j].x, elements[j].y);
              ctx.globalAlpha = 1 - distance / 100;
              ctx.stroke();
              ctx.globalAlpha = 1;
            }
          }
        }

        // Update and draw particles
        elements.forEach(particle => {
          // Update position
          particle.x += particle.speedX ?? 0;
          particle.y += particle.speedY ?? 0;

          // Bounce off edges
          if (particle.x < 0 || particle.x > canvas.width) particle.speedX = -(particle.speedX ?? 0);
          if (particle.y < 0 || particle.y > canvas.height) particle.speedY = -(particle.speedY ?? 0);

          // Draw particle
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
          ctx.fillStyle = particle.color;
          ctx.fill();
        });
      } else if (type === 'grid') {
        // Draw grid connections
        ctx.strokeStyle = primaryColor;
        ctx.lineWidth = 0.5;

        elements.forEach(gridElement => {
          ctx.beginPath();
          ctx.arc(gridElement.x, gridElement.y, gridElement.size, 0, Math.PI * 2);
          ctx.fillStyle = gridElement.color;
          ctx.fill();
        });
      } else if (type === 'hexagons') {
        // Draw hexagons
        elements.forEach(hexagon => {
          ctx.beginPath();
          drawHexagon(hexagon.x, hexagon.y, hexagon.radius ?? 0);
          ctx.fillStyle = hexagon.color;
          ctx.globalAlpha = hexagon.opacity ?? 1;
          ctx.fill();
          ctx.globalAlpha = 1;
        });
      }

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    // Start animation based on type
    if (type === 'neural') {
      animateNeural(0);
    } else {
      animate(0);
    }

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (interactive && container) {
        container.removeEventListener('mousemove', handleMouseMove);
        container.removeEventListener('mouseleave', handleMouseLeave);
      }
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [type, density, color, secondaryColor, speed, interactive, activeSection]);

  return (
    <div
      ref={containerRef}
      className={`animated-background absolute inset-0 overflow-hidden ${className}`}
    >
      <canvas ref={canvasRef} className="w-full h-full" />
    </div>
  );
};

export default AnimatedBackground;