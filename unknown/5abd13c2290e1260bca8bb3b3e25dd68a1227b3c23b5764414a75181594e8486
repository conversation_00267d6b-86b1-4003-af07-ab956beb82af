// src/app/[locale]/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import HeroSection from '@/components/sections/HeroSection';

export default async function Home({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const heroTranslations = {
    title: t('hero.headline'),
    subtitle: t('hero.subtitle'),
    cta: t('hero.cta'),
    mindMap: {
      title: t('mindMap.title'),
      expandView: t('mindMap.expandView'),
      defaultView: t('mindMap.defaultView'),
      zoomIn: t('mindMap.zoomIn'),
      zoomOut: t('mindMap.zoomOut'),
      close: t('mindMap.close'),
      keyFeatures: t('mindMap.keyFeatures'),
      keyBenefits: t('mindMap.keyBenefits'),
      learnMore: t('mindMap.learnMore'),
      nodes: t('mindMap.nodes', { returnObjects: true }),
      details: t('mindMap.details', { returnObjects: true }),
      detailNodes: t('mindMap.detailNodes', { returnObjects: true })
    }
  };

  return (
    <div>
      <HeroSection translations={heroTranslations} />
    </div>
  );
}
