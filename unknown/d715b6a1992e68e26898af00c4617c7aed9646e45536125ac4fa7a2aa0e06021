'use client';

import { motion } from 'framer-motion';
import { useRef } from 'react';
import Image from 'next/image';

interface Partner {
  name: string;
  url: string;
  logo: string;
  description: string;
  sectors: string[];
  collaboration: string;
}

interface PartnersSectionProps {
  translations?: {
    title?: string;
    subtitle?: string;
    becomePartnerTitle?: string;
    becomePartnerText?: string;
    contactButton?: string;
    visitWebsite?: string;
  };
}

const PartnersSection: React.FC<PartnersSectionProps> = ({ translations }) => {
  const sectionRef = useRef<HTMLDivElement>(null);

  const partners: Partner[] = [
    {
      name: 'AxonV',
      url: 'https://www.axonv.com',
      logo: '/images/axonv_logo.svg',
      description: 'Pioneers in machine learning applications for healthcare and robotics, developing cutting-edge AI solutions for medical diagnostics and intelligent automation.',
      sectors: ['Machine Learning', 'Healthcare', 'Robotics'],
      collaboration: 'Our partnership focuses on deploying advanced ML models in healthcare settings and developing robotics solutions for industrial automation.',
    },
    {
      name: 'Klikni<PERSON>',
      url: 'https://www.kliknime.si',
      logo: '/images/kliknime_logo.png',
      description: 'Comprehensive IT services provider offering infrastructure management, cloud solutions, and business software implementation.',
      sectors: ['IT Services', 'Cloud Computing', 'Business Software'],
      collaboration: 'We work together to deliver end-to-end IT solutions, combining our consulting expertise with their implementation capabilities.',
    },
    {
      name: 'JMD Consulting',
      url: 'https://jmd-consulting.com',
      logo: '/images/jmd_logo.png',
      description: 'Specialists in insurance and InsureTech solutions, providing innovative approaches to risk management and digital transformation in the insurance sector.',
      sectors: ['Insurance', 'InsureTech', 'Risk Management', 'Digital Transformation'],
      collaboration: 'We collaborate on digital transformation projects for insurance companies, combining our technical expertise with their domain knowledge.',
    },
  ];

  return (
    <section
      ref={sectionRef}
      className="relative min-h-screen bg-black pt-32 pb-20"
      id="partners"
    >
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
          initial={{ opacity: 0, y: 0 }} // Remove vertical animation
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          {translations?.title || 'Our Partners'}
        </motion.h2>

        <motion.p
          className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-16"
          initial={{ opacity: 0, y: 0 }} // Remove vertical animation
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {translations?.subtitle || 'We collaborate with industry leaders to deliver comprehensive solutions that drive innovation and transformation.'}
        </motion.p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {partners.map((partner, index) => (
            <motion.div
              key={index}
              className="p-6 rounded-lg border border-gray-800 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 + (index * 0.15) }}
              whileHover={{ scale: 1.03, boxShadow: "0 10px 30px -10px rgba(0, 200, 255, 0.2)", transition: { duration: 0.2 } }}
            >
              <div className="flex items-start justify-start mb-6 h-8">
                <div className={`relative w-1/4 h-full rounded p-1 ${partner.name === 'Kliknime' ? 'bg-white' : 'bg-gray-800/50'}`}>
                  <div className="relative w-full h-full">
                    <Image
                      src={partner.logo}
                      alt={`${partner.name} logo`}
                      fill
                      className="object-contain object-left"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                  </div>
                </div>
              </div>

              <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">{partner.name}</h3>

              <div className="flex flex-wrap gap-2 justify-center mb-4">
                {partner.sectors.map((sector: string, idx: number) => (
                  <span key={idx} className="px-3 py-1 bg-gray-800 text-cyan-300 text-sm rounded-full">
                    {sector}
                  </span>
                ))}
              </div>

              <p className="text-gray-300 mb-4">{partner.description}</p>

              <div className="mt-4 p-4 bg-blue-900/30 rounded-lg border border-blue-500/20">
                <h4 className="font-semibold text-cyan-400 mb-2">Collaboration</h4>
                <p className="text-sm text-blue-100">{partner.collaboration}</p>
              </div>

              <div className="mt-6">
                <a
                  href={partner.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-cyan-400 hover:text-cyan-300 flex items-center group transition-all duration-300"
                >
                  {translations?.visitWebsite || 'Visit Website'}
                  <svg className="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </a>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;