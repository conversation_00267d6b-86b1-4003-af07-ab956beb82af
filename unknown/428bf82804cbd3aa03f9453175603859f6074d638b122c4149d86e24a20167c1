'use client';

import React from 'react';
import { motion } from 'framer-motion';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost';
type ButtonSize = 'sm' | 'md' | 'lg';

type ButtonProps = {
  withArrow?: boolean; // Add this property
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: React.ReactNode;
  fullWidth?: boolean;
  isLoading?: boolean;
  hasGlow?: boolean;
  className?: string;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean; // Add this property
};

const Button: React.FC<ButtonProps> = ({
  withArrow,
  children,
  variant = 'primary',
  size = 'md',
  icon,
  fullWidth = false,
  isLoading = false,
  hasGlow = false,
  className = '',
  disabled = false, // Default value for disabled
  ...props
}) => {
  const baseStyles = 'relative inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900';
  const sizeStyles = {
    sm: 'px-2 sm:px-3 py-1 sm:py-1.5 text-xs sm:text-sm',
    md: 'px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base',
    lg: 'px-4 sm:px-6 py-2 sm:py-3 text-base sm:text-lg',
  };
  const variantStyles = {
    primary: 'bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white border-0 shadow-lg shadow-cyan-500/20',
    secondary: 'bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0 shadow-lg shadow-purple-500/20',
    outline: 'bg-transparent border border-cyan-500 text-cyan-400 hover:bg-cyan-900/20',
    ghost: 'bg-transparent hover:bg-white/10 text-white',
  };
  const glowStyles = hasGlow
    ? 'after:content-[""] after:absolute after:inset-0 after:z-[-1] after:opacity-50 after:rounded-md after:filter after:blur-xl after:bg-inherit'
    : '';
  const widthStyle = fullWidth ? 'w-full' : '';

  return (
    <motion.button
      className={`
        ${baseStyles}
        ${sizeStyles[size]}
        ${variantStyles[variant]}
        ${widthStyle}
        ${glowStyles}
        ${className}
      `}
      whileTap={{ scale: 0.97 }}
      whileHover={variant !== 'ghost' ? { y: -2 } : {}}
      disabled={isLoading || disabled} // Use the disabled prop
      {...props}
    >
      {isLoading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {icon && !isLoading && <span className="mr-2">{icon}</span>}
      {children}
      {withArrow && <span className="ml-2" aria-hidden="true">→</span>}
    </motion.button>
  );
};

export default Button;