'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import LanguageSwitcher, { LOCALES } from '@/components/common/LanguageSwitcher'
import ElysianLogo from '@/components/common/ElysianLogo'

type HeaderProps = {
  locale: string;
  translations: {
    home: string;
    services: string;
    partners: string;
    contact: string;
    selectLanguage: string;
    logo?: {
      alt: string;
      fallback: string;
    };
  };
}

export default function Header({ locale, translations }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const pathname = usePathname()

  // Handle scroll effect for header and update header height CSS variable
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10) // Reduce threshold to 10px
    }

    const updateHeaderHeight = () => {
      const header = document.querySelector('header')
      if (header) {
        // Get the exact header height
        const headerHeight = header.getBoundingClientRect().height
        // Set the CSS variable for the header height
        document.documentElement.style.setProperty('--header-height', `${headerHeight}px`)
        // Also update the nav-sticky-top variable to match the header height
        document.documentElement.style.setProperty('--nav-sticky-top', `${headerHeight}px`)
        console.log('Header height updated:', headerHeight)

        // Force a small delay to ensure the DOM has updated
        setTimeout(() => {
          // Double-check that the variables are set correctly
          const computedHeaderHeight = getComputedStyle(document.documentElement).getPropertyValue('--header-height')
          const computedNavStickyTop = getComputedStyle(document.documentElement).getPropertyValue('--nav-sticky-top')
          console.log('Computed header height:', computedHeaderHeight)
          console.log('Computed nav sticky top:', computedNavStickyTop)
        }, 100)
      }
    }

    // Initial update
    updateHeaderHeight()

    // Update on scroll
    window.addEventListener('scroll', () => {
      handleScroll()
      updateHeaderHeight()
    })

    // Update on resize
    window.addEventListener('resize', updateHeaderHeight)

    // Update on load
    window.addEventListener('load', updateHeaderHeight)

    // Set up a MutationObserver to watch for changes to the header
    const header = document.querySelector('header')
    if (header) {
      const observer = new MutationObserver(updateHeaderHeight)

      // Watch for changes to the header's attributes and children
      observer.observe(header, {
        attributes: true,
        childList: true,
        subtree: true,
        attributeFilter: ['style', 'class']
      })

      return () => {
        observer.disconnect()
        window.removeEventListener('scroll', handleScroll)
        window.removeEventListener('resize', updateHeaderHeight)
        window.removeEventListener('load', updateHeaderHeight)
      }
    }

    return () => {
      window.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', updateHeaderHeight)
      window.removeEventListener('load', updateHeaderHeight)
    }
  }, [])

  const navItems = [
    { key: 'home', href: `/${locale}`, label: translations.home },
    { key: 'services', href: `/${locale}/services`, label: translations.services },
    { key: 'partners', href: `/${locale}/partners`, label: translations.partners },
    { key: 'contact', href: `/${locale}/contact`, label: translations.contact },
  ]

  return (
    <header
      className={`fixed top-0 left-0 w-full transition-all duration-300 ${
        isScrolled
          ? 'backdrop-blur-lg'
          : 'bg-transparent'
      }`}
      style={isScrolled
        ? { backgroundColor: 'rgba(0, 0, 0, 0.9)', padding: '0', margin: '0', zIndex: 'var(--header-z-index)' }
        : { padding: '0', margin: '0', zIndex: 'var(--header-z-index)' }}
    >
      <div className="container mx-auto flex justify-between items-center" style={{ padding: '0.1rem 1rem' }}>
        {/* Updated Logo */}
        <Link href={`/${locale}`} className="group" style={{ lineHeight: 0, padding: 0, margin: 0 }}>
          <div className="hidden md:block">
            <ElysianLogo
              width={90}
              className='select-none'
              translations={translations.logo || { alt: 'Elysian Systems Logo', fallback: 'Elysian Systems' }}
            />
          </div>
          {/* Smaller logo for mobile */}
          <div className="md:hidden">
            <ElysianLogo
              width={70}
              className='select-none'
              translations={translations.logo || { alt: 'Elysian Systems Logo', fallback: 'Elysian Systems' }}
            />
          </div>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6" style={{ height: '24px', lineHeight: '24px' }}>
          {navItems.map(item => (
            <Link
              key={item.key}
              href={item.href}
              className={`font-medium uppercase tracking-wide text-sm transition-colors ${
                pathname === item.href
                  ? ''
                  : 'text-white'
              }`}
              style={
                pathname === item.href
                  ? { color: '#2dd4bf', padding: '0', lineHeight: '24px', height: '24px' }
                  : { opacity: 0.8, color: 'white', padding: '0', lineHeight: '24px', height: '24px' }
              }
            >
              {item.label}
            </Link>
          ))}

          {/* Use the dedicated LanguageSwitcher component */}
          <LanguageSwitcher
            currentLocale={locale}
            translations={{
              selectLanguage: translations.selectLanguage,
              languages: {
                'en': 'English',
                'sl': 'Slovenščina',
                'de': 'Deutsch',
                'hr': 'Hrvatski',
                'fr': 'Français',
                'it': 'Italiano',
                'es': 'Español',
                'zh': '简体中文'
              }
            }}
          />
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden flex items-center"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label="Toggle menu"
          style={{ height: '24px', padding: 0, margin: 0 }}
        >
          <div className="w-5 h-4 flex flex-col justify-between">
            <span className={`h-0.5 w-full transform transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-2' : ''}`} style={{ backgroundColor: '#2dd4bf' }} />
            <span className={`h-0.5 w-full transition-all duration-300 ${isMenuOpen ? 'opacity-0' : 'opacity-100'}`} style={{ backgroundColor: '#2dd4bf' }} />
            <span className={`h-0.5 w-full transform transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-2' : ''}`} style={{ backgroundColor: '#2dd4bf' }} />
          </div>
        </button>
      </div>

      {/* Mobile Navigation */}
      <div
        className={`md:hidden fixed inset-0 backdrop-blur-lg z-40 transition-all duration-300 ${
          isMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
        style={{ backgroundColor: 'rgba(13, 17, 23, 0.95)' }}>
        <div className="container mx-auto px-4 py-20">
          <nav className="flex flex-col space-y-8">
            {navItems.map(item => (
              <Link
                key={item.key}
                href={item.href}
                onClick={() => setIsMenuOpen(false)}
                className={`font-medium uppercase tracking-wide text-sm transition-colors ${
                  pathname === item.href
                    ? ''
                    : 'text-white'
                }`}
                style={
                  pathname === item.href
                    ? { color: '#2dd4bf' }
                    : { opacity: 0.8, color: 'white' }
                }
              >
                {item.label}
              </Link>
            ))}

            {/* Mobile Language Selector */}
            <div className="pt-4 border-t border-white" style={{ borderColor: 'rgba(255, 255, 255, 0.1)' }}>
              <p className="text-sm font-normal text-white mb-4" style={{ opacity: 0.6 }}>{translations.selectLanguage}</p>
              <div className="grid grid-cols-2 gap-2">
                {LOCALES.map((loc) => (
                  <Link
                    key={loc}
                    href={`/${loc}${pathname?.replace(`/${locale}`, '') || '/'}`}
                    onClick={() => {
                      document.cookie = `NEXT_LOCALE=${loc}; path=/; max-age=${60 * 60 * 24 * 365}`;
                      setIsMenuOpen(false);
                    }}
                    className={`py-2 px-3 rounded-md border text-center transition-colors ${
                      locale === loc
                        ? 'border-blue-500 text-blue-500'
                        : 'border-white text-white hover:border-blue-500'
                    }`}
                    style={locale === loc
                      ? { backgroundColor: 'rgba(0, 0, 0, 0.3)' }
                      : {
                          borderColor: 'rgba(255, 255, 255, 0.1)',
                          opacity: 0.8,
                          '--hover-border-color': 'rgba(59, 130, 246, 0.3)',
                          '--hover-bg-color': 'rgba(0, 0, 0, 0.2)'
                        } as React.CSSProperties}
                  >
                    {loc === 'en' ? 'English' :
                     loc === 'sl' ? 'Slovenščina' :
                     loc === 'de' ? 'Deutsch' :
                     loc === 'hr' ? 'Hrvatski' :
                     loc === 'fr' ? 'Français' :
                     loc === 'it' ? 'Italiano' :
                     loc === 'es' ? 'Español' :
                     loc === 'zh' ? '简体中文' : loc}
                  </Link>
                ))}
              </div>
            </div>
          </nav>
        </div>
      </div>
    </header>
  )
}