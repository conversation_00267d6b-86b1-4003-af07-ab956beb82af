// src/components/layout/RadialNavigation.tsx
'use client' // This stays as we need client interactivity for the menu

import { useState, useRef, useEffect, useMemo } from 'react'
import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import * as Tooltip from '@radix-ui/react-tooltip'
import { useRouter } from 'next/navigation'
import { Microscope, Building, Sparkles, UserPlus, Activity } from 'lucide-react'

type RadialNavigationProps = {
  locale: string
  translations: {
    labs: string
    architectures: string
    futures: string
    humanPlus: string
    pulse: string
  }
}

export default function RadialNavigation({ locale, translations }: RadialNavigationProps) {
  const [isOpen, setIsOpen] = useState(false)
  const orbRef = useRef<HTMLButtonElement>(null)
  const menuRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<HTMLButtonElement[]>([])
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null)
  const [activeMobileTooltip, setActiveMobileTooltip] = useState<number | null>(null)
  const router = useRouter()

  // Create refs for each menu item
  const setItemRef = (el: HTMLButtonElement | null, index: number) => {
    if (el) itemRefs.current[index] = el
  }

  // Use smaller radius for mobile devices
  const [isMobile, setIsMobile] = useState(false)
  // Use appropriate radius for the menu circle
  const radius = isMobile ? 70 : 80
  // Use smaller buttons for better visibility and less blur
  const buttonSize = isMobile ? 46 : 52

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Initial check
    checkMobile()

    // Add resize listener
    window.addEventListener('resize', checkMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Memoize menuItems to prevent unnecessary re-renders
  const menuItems = useMemo(() => [
    {
      key: 'labs',
      icon: Microscope,
      color: '#D946EF',
      href: `/${locale}/labs`,
      label: translations.labs
    },
    {
      key: 'architectures',
      icon: Building,
      color: '#3B82F6',
      href: `/${locale}/architectures`,
      label: translations.architectures
    },
    {
      key: 'futures',
      icon: Sparkles,
      color: '#9333EA',
      href: `/${locale}/futures`,
      label: translations.futures
    },
    {
      key: 'humanPlus',
      icon: UserPlus,
      color: '#14B8A6',
      href: `/${locale}/human-plus`,
      label: translations.humanPlus
    },
    {
      key: 'pulse',
      icon: Activity,
      color: 'white',
      href: `/${locale}/pulse`,
      label: translations.pulse
    },
  ], [locale, translations])

  // Keyboard navigation logic
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) {
        // Open menu on Space or Enter when orb is focused
        if ((e.key === 'Enter' || e.key === ' ') && document.activeElement === orbRef.current) {
          e.preventDefault()
          setIsOpen(true)
          // Focus the first item after opening
          setTimeout(() => setFocusedIndex(0), 100)
        }
        return
      }

      if (e.key === 'Escape') {
        // First, clear the focused index to prevent any menu item from being focused
        setFocusedIndex(null)

        // Then close the menu
        setIsOpen(false)

        // Don't try to focus the orb immediately - let the AnimatePresence finish first
      }

      if (['ArrowRight', 'ArrowDown', 'Tab'].includes(e.key) && !e.shiftKey) {
        e.preventDefault()
        setFocusedIndex(prev => prev === null ? 0 : (prev + 1) % menuItems.length)
      }

      if (['ArrowLeft', 'ArrowUp'].includes(e.key) || (e.key === 'Tab' && e.shiftKey)) {
        e.preventDefault()
        setFocusedIndex(prev => prev === null ? 0 : (prev - 1 + menuItems.length) % menuItems.length)
      }

      if ((e.key === 'Enter' || e.key === ' ') && focusedIndex !== null) {
        e.preventDefault()
        router.push(menuItems[focusedIndex].href)
        setIsOpen(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, focusedIndex, menuItems, router])

  useEffect(() => {
    if (focusedIndex !== null && itemRefs.current[focusedIndex]) {
      itemRefs.current[focusedIndex]?.focus()
    }
  }, [focusedIndex])

  // Handle focus when menu closes
  useEffect(() => {
    if (!isOpen) {
      // When menu closes, remove focus from any element
      if (document.activeElement instanceof HTMLElement) {
        document.activeElement.blur()
      }
      // Reset active mobile tooltip when menu closes
      setActiveMobileTooltip(null)
    }
  }, [isOpen])

  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(e.target as Node) &&
        orbRef.current &&
        !orbRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false)
      }
    }
    if (isOpen) document.addEventListener('mousedown', handleClick)
    return () => document.removeEventListener('mousedown', handleClick)
  }, [isOpen])

  return (
    <div className="fixed z-50" style={{ right: '16px', bottom: '16px' }}>
      {/* Orb Button */}
      <motion.button
        ref={orbRef}
        className="relative w-12 h-12 rounded-full shadow-lg flex items-center justify-center overflow-hidden cursor-pointer group z-50"
        // Define the orb size here since we removed the variable
        style={{ background: 'linear-gradient(to bottom right, #2dd4bf, #3B82F6)' }}
        whileTap={{ scale: 0.95 }}
        onClick={() => {
          setIsOpen(!isOpen)
          setFocusedIndex(null)
        }}
        aria-label="Navigation Menu"
        aria-expanded={isOpen}
        aria-controls="radial-navigation-menu"
      >
        <span className="absolute inset-0.5 rounded-full transition-all" style={{ backgroundColor: '#000000' }} />
        <span className="relative flex items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-6 w-6 transition-transform duration-300 ${isOpen ? 'rotate-45' : ''}`}
            style={{ color: '#3B82F6' }}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            {isOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
            )}
          </svg>
        </span>
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              className="fixed inset-0 backdrop-blur-sm z-40"
              style={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => {
                setIsOpen(false)
                setFocusedIndex(null)
                setActiveMobileTooltip(null)
                // Explicitly blur any active element
                if (document.activeElement instanceof HTMLElement) {
                  document.activeElement.blur()
                }
              }}
            />

            {/* Menu */}
            <motion.div
              ref={menuRef}
              id="radial-navigation-menu"
              role="menu"
              aria-orientation="horizontal"
              className="absolute z-50"
              tabIndex={-1} // Prevent the menu container from receiving focus
              style={{
                width: `${radius * 2}px`,
                height: `${radius * 2}px`,
                right: '0',  // Align with the right edge of the orb
                bottom: '0', // Align with the bottom edge of the orb
                transform: 'translate(-40%, -40%)', // Position even closer to the bottom right
                outline: 'none', // Remove outline when focused
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <Tooltip.Provider>
                {menuItems.map((item, i) => {
                  const angle = (2 * Math.PI * i) / menuItems.length - Math.PI / 2
                  const x = Math.cos(angle) * radius + radius
                  const y = Math.sin(angle) * radius + radius
                  // Improved tooltip positioning based on angle
                  let tooltipSide: 'top' | 'bottom' | 'left' | 'right'

                  // Convert angle to degrees for easier reasoning
                  const angleDeg = (angle * 180 / Math.PI) + 90

                  // Determine tooltip position based on angle and item
                  if (item.key === 'futures' || item.key === 'labs') {
                    // Always position the futures and labs tooltips on top
                    tooltipSide = 'top'
                  } else {
                    // For other items, use the angle-based positioning
                    if (angleDeg >= 315 || angleDeg < 45) tooltipSide = 'right'
                    else if (angleDeg >= 45 && angleDeg < 135) tooltipSide = 'bottom'
                    else if (angleDeg >= 135 && angleDeg < 225) tooltipSide = 'left'
                    else tooltipSide = 'top'
                  }

                  return (
                    <motion.div
                      key={item.key}
                      className="absolute"
                      style={{
                        left: `${x - buttonSize / 2}px`,
                        top: `${y - buttonSize / 2}px`,
                        width: buttonSize,
                        height: buttonSize,
                      }}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{
                        opacity: 1,
                        scale: 1,
                        transition: {
                          type: 'spring',
                          stiffness: 400,
                          damping: 17,
                          delay: 0.05 + i * 0.05,
                        }
                      }}
                      exit={{
                        opacity: 0,
                        scale: 0,
                        transition: {
                          duration: 0.2,
                          delay: 0.05 + (menuItems.length - i) * 0.03,
                        }
                      }}
                    >
                      {isMobile ? (
                        <div className="relative">
                          <button
                            ref={(el) => setItemRef(el, i)}
                            className="w-full h-full rounded-full flex items-center justify-center shadow-lg border-2 outline-none focus-visible:ring-2 focus-visible:ring-white transition-all duration-300"
                            style={{
                              backgroundColor: '#0D1117',
                              borderColor: item.color,
                              color: item.color,
                              boxShadow: `0 0 12px ${item.color}`,
                            }}
                            onClick={() => {
                              // If tooltip is already active, navigate
                              if (activeMobileTooltip === i) {
                                console.log(`Navigating to: ${item.href}`, item.key);
                                router.push(item.href);
                                setIsOpen(false);
                                setActiveMobileTooltip(null);
                              } else {
                                // Otherwise, show the tooltip
                                setActiveMobileTooltip(i);
                              }

                              // Explicitly blur the button when clicked
                              if (document.activeElement instanceof HTMLElement) {
                                document.activeElement.blur();
                              }
                            }}
                            role="menuitem"
                            tabIndex={isOpen ? 0 : -1}
                            aria-label={item.label}
                          >
                            {React.createElement(item.icon, {
                              size: 24,
                              color: item.color,
                              strokeWidth: 2
                            })}
                          </button>
                          {/* Mobile label - only show for active tooltip */}
                          {activeMobileTooltip === i && (
                            <div
                              className="absolute whitespace-nowrap px-2 py-1 rounded text-xs font-medium shadow-lg border-2 z-50 pointer-events-none"
                              style={{
                                backgroundColor: '#0D1117',
                                borderColor: item.color,
                                boxShadow: `0 0 12px ${item.color}`,
                                color: item.color,
                                bottom: tooltipSide === 'bottom' ? '-30px' : 'auto',
                                top: tooltipSide === 'top' ? '-30px' : 'auto',
                                left: tooltipSide === 'left' ? '-100%' :
                                     (tooltipSide === 'bottom' || tooltipSide === 'top') ? '50%' : 'auto',
                                right: tooltipSide === 'right' ? '-100%' : 'auto',
                                transform: (tooltipSide === 'bottom' || tooltipSide === 'top') ? 'translateX(-50%)' : 'none',
                              }}
                            >
                              {item.label}
                            </div>
                          )}
                        </div>
                      ) : (
                        <Tooltip.Root delayDuration={200}>
                          <Tooltip.Trigger asChild>
                            <div className="pointer-events-auto">
                              <button
                                ref={(el) => setItemRef(el, i)}
                                className="w-full h-full rounded-full flex items-center justify-center shadow-lg border-2 outline-none focus-visible:ring-2 focus-visible:ring-white transition-all duration-300"
                                style={{
                                  backgroundColor: '#0D1117',
                                  borderColor: item.color,
                                  color: item.color,
                                  boxShadow: `0 0 12px ${item.color}`,
                                }}
                                onClick={() => {
                                  console.log(`Navigating to: ${item.href}`, item.key);
                                  router.push(item.href);
                                  setIsOpen(false);
                                  // Explicitly blur the button when clicked
                                  if (document.activeElement instanceof HTMLElement) {
                                    document.activeElement.blur();
                                  }
                                }}
                                role="menuitem"
                                tabIndex={isOpen ? 0 : -1}
                                aria-label={item.label}
                              >
                                {React.createElement(item.icon, {
                                  size: 24,
                                  color: item.color,
                                  strokeWidth: 2
                                })}
                              </button>
                            </div>
                          </Tooltip.Trigger>
                          <Tooltip.Portal>
                            <Tooltip.Content
                              className="whitespace-nowrap px-3 py-1.5 rounded text-xs sm:text-sm font-medium shadow-lg border-2 z-50"
                              style={{
                                backgroundColor: '#0D1117',
                                borderColor: item.color,
                                boxShadow: `0 0 12px ${item.color}`,
                                color: item.color,
                              }}
                              side={tooltipSide}
                              sideOffset={5}
                              avoidCollisions
                            >
                              {item.label}
                              <Tooltip.Arrow style={{ fill: '#0D1117' }} />
                            </Tooltip.Content>
                          </Tooltip.Portal>
                        </Tooltip.Root>
                      )}
                    </motion.div>
                  )
                })}
              </Tooltip.Provider>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}