'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { motion } from 'framer-motion';

interface NodeDetailModalProps {
  nodeId?: string;
  isOpen?: boolean;
  onClose: () => void;
  nodeData?: {
    title: string;
    description: string;
    icon: string;
    features: string[];
    benefits: string[];
  };
  data?: {
    title: string;
    description: string;
    icon: string;
    features: string[];
    benefits: string[];
  };
  navLink?: string; // Navigation link for the "Learn More" button
  translations: {
    keyFeatures: string;
    keyBenefits: string;
    learnMore: string;
  };
}

const NodeDetailModal: React.FC<NodeDetailModalProps> = ({
  nodeId,
  onClose,
  nodeData,
  data,
  navLink,
  translations
}) => {
  // Use data prop if nodeData is not provided
  const displayData = nodeData || data;

  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Animation variants
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[9999]"
      onClick={handleBackdropClick}
    >
      <motion.div
        className="p-6 rounded-lg border border-white/20 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm max-w-2xl relative"
        onClick={(e) => e.stopPropagation()}
        variants={modalVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        style={{
          boxShadow: '0 0 20px rgba(99, 102, 241, 0.5), 0 0 40px rgba(99, 102, 241, 0.2), inset 0 0 15px rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
          aria-label="Close"
        >
          <X size={24} />
        </button>

        {displayData && (
          <>
            <div
              className="text-3xl mb-6 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center"
              style={{
                boxShadow: '0 0 10px rgba(6, 182, 212, 0.7), 0 0 20px rgba(168, 85, 247, 0.5), inset 0 0 5px rgba(255, 255, 255, 0.5)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                textShadow: '0 0 5px rgba(255, 255, 255, 0.8)'
              }}
            >
              <span style={{
                filter: 'brightness(1.2) contrast(1.2)',
                color: '#ffffff'
              }}>{displayData.icon}</span>
            </div>

            <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">{displayData.title}</h3>
            <p className="mb-6 text-gray-300">{displayData.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                style={{
                  boxShadow: '0 0 10px rgba(6, 182, 212, 0.3), inset 0 0 5px rgba(6, 182, 212, 0.1)',
                  background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(0, 0, 0, 0.7))'
                }}>
                <h4 className="font-semibold text-cyan-400 mb-2" style={{ textShadow: '0 0 5px rgba(6, 182, 212, 0.8)' }}>{translations.keyFeatures}</h4>
                <ul className="space-y-2 text-gray-300">
                  {displayData.features.map((feature, index) => (
                    <li key={`feature-${index}`} className="flex items-start">
                      <span className="text-cyan-400 mr-2" style={{ textShadow: '0 0 3px rgba(6, 182, 212, 0.8)' }}>→</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                style={{
                  boxShadow: '0 0 10px rgba(236, 72, 153, 0.3), inset 0 0 5px rgba(236, 72, 153, 0.1)',
                  background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(0, 0, 0, 0.7))'
                }}>
                <h4 className="font-semibold text-pink-400 mb-2" style={{ textShadow: '0 0 5px rgba(236, 72, 153, 0.8)' }}>{translations.keyBenefits}</h4>
                <ul className="space-y-2 text-gray-300">
                  {displayData.benefits.map((benefit, index) => (
                    <li key={`benefit-${index}`} className="flex items-start">
                      <span className="text-pink-400 mr-2" style={{ textShadow: '0 0 3px rgba(236, 72, 153, 0.8)' }}>→</span>
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </>
        )}

        <div className="mt-4">
          {navLink ? (
            <button
              className="text-cyan-400 hover:text-white flex items-center group transition-all duration-300"
              onClick={(e) => {
                e.stopPropagation();

                // Get the current locale from the URL
                const currentLocale = window.location.pathname.split('/')[1];

                // Construct the path with the current locale
                const path = `/${currentLocale}/${navLink}`;
                console.log(`Navigating to: ${path} from Learn More button`);

                // Store the section ID in localStorage so the services page can select it
                if (nodeId) {
                  const sectionMap: Record<string, string> = {
                    'insurance': 'insurance',
                    'technology': 'it',
                    'ml': 'ml',
                    'consulting': 'consulting',
                    'enterprise': 'it'
                  };

                  const sectionId = sectionMap[nodeId] || '';
                  if (sectionId) {
                    localStorage.setItem('activeServiceSection', sectionId);
                    console.log(`Stored section ID: ${sectionId} in localStorage`);
                  }
                }

                // Navigate to the specified path
                window.location.href = path;

                // Close the modal
                onClose();
              }}
              style={{
                textShadow: '0 0 5px rgba(6, 182, 212, 0.8)',
                position: 'relative'
              }}
            >
              <span className="relative z-10">{translations.learnMore}</span>
              <svg
                className="ml-1 h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                style={{
                  filter: 'drop-shadow(0 0 2px rgba(6, 182, 212, 0.8))'
                }}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 rounded-md blur-md transition-opacity duration-300"></span>
            </button>
          ) : null}
        </div>
      </motion.div>
    </div>
  );
};

export default NodeDetailModal;
