{"name": "elysian-systems", "homepage": "https://elysian-systems.com", "version": "1.0.0", "private": true, "scripts": {"predev": "cross-env USE_TURBOPACK=false tsx setup-config.ts", "dev": "next dev", "predev:turbo": "cross-env USE_TURBOPACK=true tsx setup-config.ts", "dev:turbo": "next dev --turbo", "dev:webpack": "next dev", "prebuild": "cross-env USE_TURBOPACK=false tsx setup-config.ts", "build": "next build && tsc --project tsconfig.server.json", "prestart": "cross-env USE_TURBOPACK=false tsx setup-config.ts", "nextstart": "next start", "start": "cross-env NODE_ENV=production node dist/server.js", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/utilities": "^3.2.2", "@next/bundle-analyzer": "^15.3.1", "@radix-ui/react-tooltip": "^1.2.6", "@sentry/nextjs": "^9.15.0", "accept-language": "^3.0.20", "framer-motion": "^12.12.1", "i18next": "^25.0.2", "i18next-http-backend": "^3.0.2", "i18next-resources-to-backend": "^1.2.1", "lucide-react": "^0.507.0", "next": "^15.3.1", "next-i18next": "^15.4.2", "nodemailer": "^7.0.3", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "react-server-dom-webpack": "^19.1.0", "react-simple-typewriter": "^5.0.1", "react-tooltip": "^5.28.1", "sharp": "^0.34.1", "swr": "^2.3.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss-load-config": "^6.0.1", "tailwindcss": "^4.1.4", "tailwindcss-cli": "^0.1.2", "tsx": "^4.19.4", "typescript": "^5"}}