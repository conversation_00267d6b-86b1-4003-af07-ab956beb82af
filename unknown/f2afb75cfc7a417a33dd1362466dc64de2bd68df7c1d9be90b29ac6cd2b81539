import { getServerTranslations } from '@/lib/i18n';
import LabsPageClient from '@/components/sections/labs/LabsPageClient';

export default async function LabsPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const labsTranslations = {
    title: t('labs.title'),
    subtitle: t('labs.subtitle'),
    cryptoBot: {
      title: t('labs.cryptoBot.title'),
      subtitle: t('labs.cryptoBot.subtitle'),
      overview: {
        title: t('labs.cryptoBot.overview.title'),
        description: t('labs.cryptoBot.overview.description')
      },
      techStack: {
        title: t('labs.cryptoBot.techStack.title'),
        components: {
          optimizationEngine: {
            title: t('labs.cryptoBot.techStack.components.optimizationEngine.title'),
            description: t('labs.cryptoBot.techStack.components.optimizationEngine.description')
          },
          featureSelection: {
            title: t('labs.cryptoBot.techStack.components.featureSelection.title'),
            description: t('labs.cryptoBot.techStack.components.featureSelection.description')
          },
          parameterOptimization: {
            title: t('labs.cryptoBot.techStack.components.parameterOptimization.title'),
            description: t('labs.cryptoBot.techStack.components.parameterOptimization.description')
          },
          technicalIndicators: {
            title: t('labs.cryptoBot.techStack.components.technicalIndicators.title'),
            description: t('labs.cryptoBot.techStack.components.technicalIndicators.description')
          },
          riskManagement: {
            title: t('labs.cryptoBot.techStack.components.riskManagement.title'),
            description: t('labs.cryptoBot.techStack.components.riskManagement.description')
          },
          performanceMetrics: {
            title: t('labs.cryptoBot.techStack.components.performanceMetrics.title'),
            description: t('labs.cryptoBot.techStack.components.performanceMetrics.description')
          }
        }
      }
    }
  };

  return <LabsPageClient translations={labsTranslations} />;
}
