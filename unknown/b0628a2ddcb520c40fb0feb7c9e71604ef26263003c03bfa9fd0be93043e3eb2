'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import { motion } from 'framer-motion';

interface MLComponentDetailTranslations {
  keyFeatures: string;
  benefits: string;
  implementationTech: string;
  technicalConsiderations: string;
  componentWorkflow: string;
  implementationArchitecture: string;
  comingSoon: string;
  learnMore: string;
  technicalConsiderationsList: string[];
}

interface MLComponentDetailProps {
  component: {
    id: string;
    name: string;
    description: string;
    icon?: string;
    color?: string;
    features?: string[];
    benefits?: string[];
    technologies?: string[];
  } | null;
  viewMode: 'conceptual' | 'implementation';
  onClose: () => void;
  translations: MLComponentDetailTranslations;
}

const MLComponentDetail: React.FC<MLComponentDetailProps> = ({
  component,
  viewMode,
  onClose,
  translations
}) => {
  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Animation variants
  const modalVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500
      }
    },
    exit: {
      opacity: 0,
      y: 20,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  if (!component) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60]"
      onClick={handleBackdropClick}
    >
      <motion.div
        className="p-6 rounded-lg border-2 bg-gradient-to-br from-[#0A1020] to-[#121826]/90 backdrop-blur-lg max-w-2xl w-full relative shadow-2xl"
        style={{
          borderColor: component?.color || '#7C4DFF',
          boxShadow: `0 0 30px ${component?.color || '#7C4DFF'}40`
        }}
        onClick={(e) => e.stopPropagation()}
        variants={modalVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 rounded-full transition-all duration-300 z-10"
          style={{
            background: `${component?.color || '#7C4DFF'}20`,
            border: `1px solid ${component?.color || '#7C4DFF'}40`,
            color: component?.color || '#7C4DFF',
            boxShadow: `0 0 10px ${component?.color || '#7C4DFF'}30`
          }}
          aria-label="Close"
        >
          <X size={20} />
        </button>

        <div className="flex items-start gap-5">
          <div
            className="text-3xl md:text-4xl font-bold rounded-full h-16 w-16 flex items-center justify-center border-2"
            style={{
              borderColor: 'white',
              background: `radial-gradient(circle at center, ${component.color}60, ${component.color}30)`,
              boxShadow: `0 0 20px ${component.color}80, inset 0 0 10px ${component.color}40`,
              filter: 'url(#strongGlow)'
            }}
          >
            <span style={{ filter: 'drop-shadow(0 0 5px white)' }}>{component.icon}</span>
          </div>

          <div className="flex-1">
            <h3
              className="text-xl md:text-2xl font-bold mb-2 font-montserrat"
              style={{
                color: component.color,
                textShadow: `0 0 10px ${component.color}80`
              }}
            >
              {component.name}
            </h3>
            <p className="mb-4 text-base font-normal text-gray-200 leading-relaxed font-open-sans">{component.description}</p>
          </div>
        </div>

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-5">
          {viewMode === 'conceptual' ? (
            <>
              <div
                className="p-5 rounded-lg border-2 shadow-lg backdrop-blur-sm"
                style={{
                  background: `linear-gradient(135deg, ${component.color}10, ${component.color}05)`,
                  borderColor: `${component.color}40`,
                  boxShadow: `0 0 20px ${component.color}20`
                }}
              >
                <h4
                  className="text-base md:text-lg font-bold mb-3 font-montserrat"
                  style={{
                    color: component.color,
                    textShadow: `0 0 8px ${component.color}40`
                  }}
                >
                  {translations.keyFeatures}
                </h4>
                <ul className="space-y-3 text-gray-200 font-open-sans">
                  {component.features?.map((feature, index) => (
                    <li key={`feature-${index}`} className="flex items-start">
                      <span
                        className="flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mr-3 mt-0.5"
                        style={{
                          background: `${component.color}30`,
                          border: `1px solid ${component.color}60`
                        }}
                      >
                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5 12L10 17L19 8" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </span>
                      <span className="leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div
                className="p-5 rounded-lg border-2 shadow-lg backdrop-blur-sm"
                style={{
                  background: `linear-gradient(135deg, ${component.color}10, ${component.color}05)`,
                  borderColor: `${component.color}40`,
                  boxShadow: `0 0 20px ${component.color}20`
                }}
              >
                <h4
                  className="text-base md:text-lg font-bold mb-3 font-montserrat"
                  style={{
                    color: component.color,
                    textShadow: `0 0 8px ${component.color}40`
                  }}
                >
                  {translations.benefits}
                </h4>
                <ul className="space-y-3 text-gray-200 font-open-sans">
                  {component.benefits?.map((benefit, index) => (
                    <li key={`benefit-${index}`} className="flex items-start">
                      <span
                        className="flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mr-3 mt-0.5"
                        style={{
                          background: `${component.color}30`,
                          border: `1px solid ${component.color}60`
                        }}
                      >
                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5 12L10 17L19 8" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </span>
                      <span className="leading-relaxed">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </>
          ) : (
            <>
              <div
                className="p-5 rounded-lg border-2 shadow-lg backdrop-blur-sm"
                style={{
                  background: `linear-gradient(135deg, ${component.color}10, ${component.color}05)`,
                  borderColor: `${component.color}40`,
                  boxShadow: `0 0 20px ${component.color}20`
                }}
              >
                <h4
                  className="text-base md:text-lg font-bold mb-3 font-montserrat"
                  style={{
                    color: component.color,
                    textShadow: `0 0 8px ${component.color}40`
                  }}
                >
                  {translations.implementationTech}
                </h4>
                <div className="flex flex-wrap gap-2 mt-3">
                  {component.technologies?.map((tech, index) => (
                    <span
                      key={`tech-${index}`}
                      className="px-3 py-1.5 rounded-full text-sm font-medium"
                      style={{
                        background: `${component.color}20`,
                        border: `1px solid ${component.color}40`,
                        color: 'white'
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
              <div className="p-4 bg-black/50 rounded-lg border border-gray-800">
                <h4 className="text-base md:text-lg font-semibold text-cyan-400 mb-2">{translations.technicalConsiderations}</h4>
                <ul className="space-y-2 text-gray-300">
                  {translations.technicalConsiderationsList.map((item, index) => (
                    <li key={`tc-${index}`} className="flex items-start">
                      <span className="text-cyan-400 mr-2">→</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </>
          )}
        </div>

        {/* Enhanced workflow visualization placeholder */}
        <div
          className="mt-8 p-5 rounded-lg border-2 shadow-lg backdrop-blur-sm"
          style={{
            background: `linear-gradient(135deg, ${component.color}10, ${component.color}05)`,
            borderColor: `${component.color}40`,
            boxShadow: `0 0 20px ${component.color}20`
          }}
        >
          <h4
            className="text-base md:text-lg font-bold mb-3 font-montserrat"
            style={{
              color: component.color,
              textShadow: `0 0 8px ${component.color}40`
            }}
          >
            {viewMode === 'conceptual' ? translations.componentWorkflow : translations.implementationArchitecture}
          </h4>
          <div
            className="h-40 flex items-center justify-center rounded-lg overflow-hidden relative"
            style={{
              background: `linear-gradient(to right, ${component.color}10, ${component.color}20, ${component.color}10)`,
              border: `1px solid ${component.color}30`
            }}
          >
            {/* Animated grid background */}
            <div
              className="absolute inset-0 opacity-30"
              style={{
                backgroundImage: `linear-gradient(${component.color}20 1px, transparent 1px), linear-gradient(90deg, ${component.color}20 1px, transparent 1px)`,
                backgroundSize: '20px 20px',
                backgroundPosition: 'center center',
                animation: 'gridMove 20s linear infinite'
              }}
            />

            {/* Pulsing circle */}
            <div
              className="relative z-10 flex flex-col items-center justify-center"
              style={{
                animation: 'pulse 2s infinite'
              }}
            >
              <div
                className="w-16 h-16 rounded-full flex items-center justify-center mb-3"
                style={{
                  background: `radial-gradient(circle at center, ${component.color}40, ${component.color}10)`,
                  border: `2px solid ${component.color}60`,
                  boxShadow: `0 0 15px ${component.color}40`
                }}
              >
                <span className="text-2xl">{component.icon}</span>
              </div>
              <p
                className="text-white text-sm font-medium px-4 py-2 rounded-full"
                style={{
                  background: `${component.color}30`,
                  border: `1px solid ${component.color}50`,
                  boxShadow: `0 0 10px ${component.color}30`
                }}
              >
                {translations.comingSoon}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <button
            className="flex items-center group transition-all duration-300 text-sm md:text-base font-medium px-5 py-2.5 rounded-full"
            style={{
              background: `linear-gradient(to right, ${component.color}70, ${component.color}40)`,
              color: 'white',
              boxShadow: `0 0 15px ${component.color}30`,
              border: `1px solid ${component.color}80`
            }}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            {translations.learnMore}
            <svg
              className="ml-2 h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Add keyframe animations */}
        <style jsx>{`
          @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
          }

          @keyframes gridMove {
            0% { background-position: 0 0; }
            100% { background-position: 40px 40px; }
          }
        `}</style>
      </motion.div>
    </div>
  );
};

export default MLComponentDetail;
