'use client';

import React from 'react';
import ProductManagementConsultation from './ProductManagementConsultation';
import ProductManagement from './ProductManagementSection';

interface HumanPlusPageContentProps {
  locale: string;
  translations: {
    productManagement: {
      title: string;
      subtitle: string;
      introduction?: string;
      cta?: string;
      cards: {
        strategy: {
          title: string;
          description: string;
          features: string[];
          cta: string;
        };
        agile: {
          title: string;
          description: string;
          features: string[];
          cta: string;
        };
        leadership: {
          title: string;
          description: string;
          features: string[];
          cta: string;
        };
        design: {
          title: string;
          description: string;
          features: string[];
          cta: string;
        };
        analytics: {
          title: string;
          description: string;
          features: string[];
          cta: string;
        };
      };
      caseStudy?: {
        title: string;
        brief: string;
        metrics: string[];
        cta: string;
      };
      contact?: {
        title: string;
        description: string;
        primaryCta: string;
        secondaryCta: string;
      };
    };
    productManagementSection: {
      title: string;
      subtitle: string;
      exploreButton: string;
      managementPhases: {
        productDevelopment: {
          title: string;
          description: string;
          processes: {
            productFeasibility: string;
            developmentMedical: string;
            rAndD: string;
            productRoadmap: string;
          };
        };
        marketStrategy: {
          title: string;
          description: string;
          processes: {
            productPositioning: string;
            competitorAnalysis: string;
            licensingStrategy: string;
            roadmapPlanning: string;
            yearlyPlanning: string;
          };
        };
        launchPreparation: {
          title: string;
          description: string;
          processes: {
            marketingStrategy: string;
            launchPreparation: string;
            communicationPlan: string;
            organizationalChart: string;
            changeManagement: string;
          };
        };
        postMarket: {
          title: string;
          description: string;
          processes: {
            postMarketSurveillance: string;
            roadmapReleases: string;
            changeManagement: string;
            communicationPlan: string;
          };
        };
      };
      components: {
        roadmapPlanning: {
          title: string;
          description: string;
          elements: {
            feasibility: {
              title: string;
              description: string;
            };
            positioning: {
              title: string;
              description: string;
            };
            releases: {
              title: string;
              description: string;
            };
            planning: {
              title: string;
              description: string;
            };
          };
        };
      };
    };
  };
}

export default function HumanPlusPageContent({ locale, translations }: HumanPlusPageContentProps) {
  return (
    <div className="md:pt-0 pt-16">
      {/* First section - Product Management Consultation */}
      <ProductManagementConsultation locale={locale} translations={translations.productManagement} />

      {/* Second section - Interactive Product Management Framework */}
      <ProductManagement translations={translations.productManagementSection} />
    </div>
  );
}
