// src/app/[locale]/human-plus/page.tsx
import { getServerTranslations } from '@/lib/i18n';
import HumanPlusPageContent from '@/components/sections/humanPlus/HumanPlusPageContent';

export default async function HumanPlusPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations for ProductManagementSection
  const productManagementSectionTranslations = {
    title: t('humanPlus.productManagementSection.title'),
    subtitle: t('humanPlus.productManagementSection.subtitle'),
    exploreButton: t('humanPlus.productManagementSection.exploreButton'),
    managementPhases: {
      productDevelopment: {
        title: t('humanPlus.productManagementSection.managementPhases.productDevelopment.title'),
        description: t('humanPlus.productManagementSection.managementPhases.productDevelopment.description'),
        processes: {
          productFeasibility: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.productFeasibility'),
          developmentMedical: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.developmentMedical'),
          rAndD: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.rAndD'),
          productRoadmap: t('humanPlus.productManagementSection.managementPhases.productDevelopment.processes.productRoadmap')
        }
      },
      marketStrategy: {
        title: t('humanPlus.productManagementSection.managementPhases.marketStrategy.title'),
        description: t('humanPlus.productManagementSection.managementPhases.marketStrategy.description'),
        processes: {
          productPositioning: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.productPositioning'),
          competitorAnalysis: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.competitorAnalysis'),
          licensingStrategy: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.licensingStrategy'),
          roadmapPlanning: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.roadmapPlanning'),
          yearlyPlanning: t('humanPlus.productManagementSection.managementPhases.marketStrategy.processes.yearlyPlanning')
        }
      },
      launchPreparation: {
        title: t('humanPlus.productManagementSection.managementPhases.launchPreparation.title'),
        description: t('humanPlus.productManagementSection.managementPhases.launchPreparation.description'),
        processes: {
          marketingStrategy: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.marketingStrategy'),
          launchPreparation: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.launchPreparation'),
          communicationPlan: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.communicationPlan'),
          organizationalChart: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.organizationalChart'),
          changeManagement: t('humanPlus.productManagementSection.managementPhases.launchPreparation.processes.changeManagement')
        }
      },
      postMarket: {
        title: t('humanPlus.productManagementSection.managementPhases.postMarket.title'),
        description: t('humanPlus.productManagementSection.managementPhases.postMarket.description'),
        processes: {
          postMarketSurveillance: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.postMarketSurveillance'),
          roadmapReleases: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.roadmapReleases'),
          changeManagement: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.changeManagement'),
          communicationPlan: t('humanPlus.productManagementSection.managementPhases.postMarket.processes.communicationPlan')
        }
      }
    },
    components: {
      keyProcesses: t('humanPlus.productManagementSection.components.keyProcesses'),
      keyFeatures: t('humanPlus.productManagementSection.components.keyFeatures'),
      roadmapPlanning: {
        title: t('humanPlus.productManagementSection.components.roadmapPlanning.title'),
        description: t('humanPlus.productManagementSection.components.roadmapPlanning.description'),
        elements: {
          feasibility: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.feasibility.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.feasibility.description')
          },
          positioning: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.positioning.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.positioning.description')
          },
          releases: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.releases.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.releases.description')
          },
          planning: {
            title: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.planning.title'),
            description: t('humanPlus.productManagementSection.components.roadmapPlanning.elements.planning.description')
          }
        }
      },
      processDetailsContent: {
        productFeasibility: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.productFeasibility.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.productFeasibility.features')
        },
        developmentMedical: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.developmentMedical.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.developmentMedical.features')
        },
        rAndD: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.rAndD.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.rAndD.features')
        },
        productRoadmap: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.productRoadmap.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.productRoadmap.features')
        },
        productPositioning: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.productPositioning.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.productPositioning.features')
        },
        competitorAnalysis: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.competitorAnalysis.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.competitorAnalysis.features')
        },
        licensingStrategy: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.licensingStrategy.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.licensingStrategy.features')
        },
        yearlyPlanning: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.yearlyPlanning.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.yearlyPlanning.features')
        },
        marketingStrategy: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.marketingStrategy.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.marketingStrategy.features')
        },
        launchPreparation: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.launchPreparation.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.launchPreparation.features')
        },
        communicationPlan: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.communicationPlan.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.communicationPlan.features')
        },
        organizationalChart: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.organizationalChart.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.organizationalChart.features')
        },
        changeManagement: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.changeManagement.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.changeManagement.features')
        },
        postMarketSurveillance: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.postMarketSurveillance.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.postMarketSurveillance.features')
        },
        roadmapReleases: {
          description: t('humanPlus.productManagementSection.components.processDetailsContent.roadmapReleases.description'),
          features: t('humanPlus.productManagementSection.components.processDetailsContent.roadmapReleases.features')
        }
      },
      processDetails: {
        closeButton: t('humanPlus.productManagementSection.components.processDetails.closeButton'),
        implementationBenefits: t('humanPlus.productManagementSection.components.processDetails.implementationBenefits'),
        contentNotAvailable: t('humanPlus.productManagementSection.components.processDetails.contentNotAvailable'),
        defaultFallbackDescription: t('humanPlus.productManagementSection.components.processDetails.defaultFallbackDescription'),
        defaultBenefits: t('humanPlus.productManagementSection.components.processDetails.defaultBenefits'),
        defaultFeatures: t('humanPlus.productManagementSection.components.processDetails.defaultFeatures')
      }
    }
  };

  // Prepare translations for ProductManagementConsultation
  const productManagementConsultationTranslations = {
    title: t('humanPlus.productManagement.title'),
    subtitle: t('humanPlus.productManagement.subtitle'),
    introduction: t('humanPlus.productManagement.introduction'),
    cta: t('humanPlus.productManagement.cta'),
    cards: {
      strategy: {
        title: t('humanPlus.productManagement.cards.strategy.title'),
        description: t('humanPlus.productManagement.cards.strategy.description'),
        features: [
          t('humanPlus.productManagement.cards.strategy.features.0'),
          t('humanPlus.productManagement.cards.strategy.features.1'),
          t('humanPlus.productManagement.cards.strategy.features.2'),
          t('humanPlus.productManagement.cards.strategy.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.strategy.cta'),
      },
      agile: {
        title: t('humanPlus.productManagement.cards.agile.title'),
        description: t('humanPlus.productManagement.cards.agile.description'),
        features: [
          t('humanPlus.productManagement.cards.agile.features.0'),
          t('humanPlus.productManagement.cards.agile.features.1'),
          t('humanPlus.productManagement.cards.agile.features.2'),
          t('humanPlus.productManagement.cards.agile.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.agile.cta'),
      },
      leadership: {
        title: t('humanPlus.productManagement.cards.leadership.title'),
        description: t('humanPlus.productManagement.cards.leadership.description'),
        features: [
          t('humanPlus.productManagement.cards.leadership.features.0'),
          t('humanPlus.productManagement.cards.leadership.features.1'),
          t('humanPlus.productManagement.cards.leadership.features.2'),
          t('humanPlus.productManagement.cards.leadership.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.leadership.cta'),
      },
      design: {
        title: t('humanPlus.productManagement.cards.design.title'),
        description: t('humanPlus.productManagement.cards.design.description'),
        features: [
          t('humanPlus.productManagement.cards.design.features.0'),
          t('humanPlus.productManagement.cards.design.features.1'),
          t('humanPlus.productManagement.cards.design.features.2'),
          t('humanPlus.productManagement.cards.design.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.design.cta'),
      },
      analytics: {
        title: t('humanPlus.productManagement.cards.analytics.title'),
        description: t('humanPlus.productManagement.cards.analytics.description'),
        features: [
          t('humanPlus.productManagement.cards.analytics.features.0'),
          t('humanPlus.productManagement.cards.analytics.features.1'),
          t('humanPlus.productManagement.cards.analytics.features.2'),
          t('humanPlus.productManagement.cards.analytics.features.3'),
        ],
        cta: t('humanPlus.productManagement.cards.analytics.cta'),
      },
    },
    caseStudy: {
      title: t('humanPlus.productManagement.caseStudy.title'),
      brief: t('humanPlus.productManagement.caseStudy.brief'),
      metrics: [
        t('humanPlus.productManagement.caseStudy.metrics.0'),
        t('humanPlus.productManagement.caseStudy.metrics.1'),
        t('humanPlus.productManagement.caseStudy.metrics.2'),
      ],
      cta: t('humanPlus.productManagement.caseStudy.cta'),
    },
    contact: {
      title: t('humanPlus.productManagement.contact.title'),
      description: t('humanPlus.productManagement.contact.description'),
      primaryCta: t('humanPlus.productManagement.contact.primaryCta'),
      secondaryCta: t('humanPlus.productManagement.contact.secondaryCta'),
    },
  };

  // Combine translations into a single object
  const translations = {
    productManagement: productManagementConsultationTranslations,
    productManagementSection: productManagementSectionTranslations
  };

  return <HumanPlusPageContent locale={resolvedParams.locale} translations={translations} />;
}
