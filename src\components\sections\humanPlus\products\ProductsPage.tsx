'use client';

import { useState, useEffect, useRef, ReactNode } from 'react';
import { motion } from 'framer-motion';
import ProductDevelopmentSection from './ProductDevelopmentSection';
import MarketStrategySection from './MarketStrategySection';
import LaunchPreparationSection from './LaunchPreparationSection';
import PostMarketSection from './PostMarketSection';

// Component that resets scroll position when it mounts
const ScrollResetter = ({ children }: { children: ReactNode }) => {
  // This component will be remounted whenever the key changes
  // When it mounts, it will be at the top of its container
  return <div className="scroll-resetter">{children}</div>;
};

type Phase = {
  id: string;
  title: string;
};

type ProductsPageProps = {
  translations?: {
    title?: string;
    subtitle?: string;
    exploreButton?: string;
    managementPhases?: {
      productDevelopment?: {
        title?: string;
        description?: string;
        processes?: {
          productFeasibility?: string;
          developmentMedical?: string;
          rAndD?: string;
          productRoadmap?: string;
        };
      };
      marketStrategy?: {
        title?: string;
        description?: string;
        processes?: {
          productPositioning?: string;
          competitorAnalysis?: string;
          licensingStrategy?: string;
          roadmapPlanning?: string;
          yearlyPlanning?: string;
        };
      };
      launchPreparation?: {
        title?: string;
        description?: string;
        processes?: {
          marketingStrategy?: string;
          launchPreparation?: string;
          communicationPlan?: string;
          organizationalChart?: string;
          changeManagement?: string;
        };
      };
      postMarket?: {
        title?: string;
        description?: string;
        processes?: {
          postMarketSurveillance?: string;
          roadmapReleases?: string;
          changeManagement?: string;
          communicationPlan?: string;
        };
      };
    };
    components?: {
      keyProcesses?: string;
      processDetailsTitle?: string;
      keyFeatures?: string;
      keyElements?: string;
      roadmapDevelopmentProcess?: string;
      roadmapDevelopmentSteps?: string[];
      strategicRoadmapFramework?: string;
      marketAnalysis?: {
        title?: string;
        items?: string[];
      };
      strategicPlanning?: {
        title?: string;
        items?: string[];
      };
      roadmapExecution?: {
        title?: string;
        releaseTimeline?: string;
        phasedImplementation?: string;
        milestoneTracking?: string;
        continuousFeedback?: string;
      };
      businessImpact?: {
        title?: string;
        impacts?: string[];
      };
      tryRoadmapTool?: string;
      roadmapToolDescription?: string;
      roadmapPlanning?: {
        title?: string;
        description?: string;
        elements?: {
          feasibility?: {
            title?: string;
            description?: string;
          };
          positioning?: {
            title?: string;
            description?: string;
          };
          releases?: {
            title?: string;
            description?: string;
          };
          planning?: {
            title?: string;
            description?: string;
          };
        };
      };
      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
      processDetails?: {
        closeButton?: string;
        implementationBenefits?: string;
        contentNotAvailable?: string;
        defaultFallbackDescription?: string;
        defaultBenefits?: string[];
        defaultFeatures?: string[];
      };
    };
  };
};

export default function ProductsPage({ translations }: ProductsPageProps) {
  const [activePhase, setActivePhase] = useState<string>('productDevelopment');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const contentSectionRef = useRef<HTMLDivElement>(null);

  // Set initial phase to productDevelopment (Product Development & Feasibility)
  useEffect(() => {
    setActivePhase('productDevelopment');
  }, []);

  // Handle phase change
  const handlePhaseChange = (phase: string) => {
    setActivePhase(phase);
    setIsMobileMenuOpen(false);

    // After the phase changes, scroll the content section to top (but keep nav visible)
    setTimeout(() => {
      if (contentSectionRef.current) {
        // Only scroll if the nav is visible (i.e., user has scrolled past header)
        const nav = document.getElementById('product-navigation');
        if (nav) {
          const navRect = nav.getBoundingClientRect();
          const navStickyTop = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--nav-sticky-top') || '0', 10);
          // If nav is at or near the top of the viewport, scroll content just below it
          if (navRect.top >= 10 && navRect.bottom > 0) {
            // Scroll the content section so its top aligns just below the nav
            const contentRect = contentSectionRef.current.getBoundingClientRect();
            const scrollY = window.scrollY + contentRect.top - navRect.height - navStickyTop;
            window.scrollTo({ top: scrollY, behavior: 'instant' in window ? 'instant' : 'auto' });
          }
        }
      }
    }, 0);
  };

  // Render the active phase based on state
  const renderActivePhase = () => {
    switch (activePhase) {
      case 'productDevelopment':
        return <ProductDevelopmentSection translations={translations} />;
      case 'marketStrategy':
        return <MarketStrategySection translations={translations} />;
      case 'launchPreparation':
        return <LaunchPreparationSection translations={translations} />;
      case 'postMarket':
        return <PostMarketSection translations={translations} />;
      default:
        return <ProductDevelopmentSection translations={translations} />;
    }
  };

  // Define phases for navigation
  const phases: Phase[] = [
    {
      id: 'productDevelopment',
      title: translations?.managementPhases?.productDevelopment?.title || 'Product Development & Feasibility'
    },
    {
      id: 'marketStrategy',
      title: translations?.managementPhases?.marketStrategy?.title || 'Market Strategy & Planning'
    },
    {
      id: 'launchPreparation',
      title: translations?.managementPhases?.launchPreparation?.title || 'Launch Preparation & Execution'
    },
    {
      id: 'postMarket',
      title: translations?.managementPhases?.postMarket?.title || 'Post-Market Surveillance & Optimization'
    }
  ];

  // Navigation component
  const ProductsNavigation = () => (
    <div
      id="product-navigation"
      className="sticky w-full left-0 right-0 bg-black/90 backdrop-blur-lg py-3 border-b border-gray-800"
      style={{
        position: 'sticky',
        top: 'var(--nav-sticky-top)',
        zIndex: 'var(--service-nav-z-index)'
      }}
    >
      <div className="container mx-auto px-4">
        {/* Mobile dropdown for smaller screens */}
        <div className="block md:hidden">
          <div
            className="flex items-center justify-between py-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="text-cyan-400 font-medium">
              {phases.find(p => p.id === activePhase)?.title || 'Select Phase'}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 text-cyan-400 transition-transform ${isMobileMenuOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>

          {/* Mobile dropdown menu */}
          {isMobileMenuOpen && (
            <div className="absolute left-0 right-0 mt-1 py-2 px-4 bg-gray-900/95 backdrop-blur-lg shadow-lg z-50">
              {phases.map((phase) => (
                <div
                  key={phase.id}
                  className="relative"
                  onClick={() => handlePhaseChange(phase.id)}
                >
                  <div
                    className={`px-4 py-3 text-base font-medium transition-all duration-300 cursor-pointer ${
                      activePhase === phase.id
                        ? 'text-cyan-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {phase.title}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:block">
          <div className="flex flex-wrap justify-center gap-2 md:gap-8">
            {phases.map((phase) => (
              <div
                key={phase.id}
                className="relative"
              >
                <div
                  onClick={() => handlePhaseChange(phase.id)}
                  className={`px-4 py-2 text-button font-medium transition-all duration-300 cursor-pointer ${
                    activePhase === phase.id
                      ? 'text-cyan-400'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {phase.title}
                </div>
                {activePhase === phase.id && (
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500"
                    layoutId="activePhase"
                    transition={{ type: 'spring', stiffness: 380, damping: 30 }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <>
      {/* Header section with title and description */}
      <section className="relative bg-black pt-8 pb-8 products-page">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-2xl md:text-4xl font-bold mb-3 md:mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {translations?.title || 'Product Management'}
          </motion.h2>

          <motion.p
            className="text-base md:text-lg text-gray-300 max-w-3xl mx-auto text-center mb-4 md:mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations?.subtitle || 'Comprehensive product management framework for medical and healthcare products. From concept to market surveillance, our solution guides you through the entire product lifecycle.'}
          </motion.p>
        </div>
      </section>

      {/* Integrated navigation section */}
      <ProductsNavigation />

      {/* Content section */}
      <section className="relative bg-black pt-4 md:pt-8 pb-20">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          {/* Active phase section - using key to force remount when phase changes */}
          <div id="product-content" className="w-full" ref={contentSectionRef}>
            {/* Using key to force the component to remount when active phase changes */}
            <ScrollResetter key={activePhase}>
              {renderActivePhase()}
            </ScrollResetter>
          </div>
        </div>
      </section>
    </>
  );
}
