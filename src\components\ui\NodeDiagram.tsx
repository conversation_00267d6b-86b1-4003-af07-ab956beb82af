'use client';
import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';

export type NodeData = {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  color?: string;
  position: { x: number; y: number };
  connections?: string[];
  size?: 'sm' | 'md' | 'lg';
  type?: 'milestone' | 'task' | 'phase';
};

export type NodeDiagramTranslations = {
  phase: string;
  task: string;
  milestone: string;
  clickToEdit: string;
  clickToConnect: string;
  connect: string;
  delete: string;
  deleteConfirm: string;
};

export type NodeDiagramProps = {
  nodes: NodeData[];
  activeNode?: string | null;
  onNodeClick: (node: NodeData) => void;
  onNodeMove?: (id: string, position: { x: number; y: number }) => void;
  onAddConnection?: (sourceId: string, targetId: string) => void;
  onDeleteNode?: (id: string) => void;
  translations: NodeDiagramTranslations;
};

const Node = ({
  node,
  active,
  onClick,
  onMove,
  translations
}: {
  node: NodeData;
  active: boolean;
  onClick: () => void;
  onMove?: (position: { x: number; y: number }) => void;
  translations: Pick<NodeDiagramTranslations, 'phase' | 'task' | 'milestone' | 'clickToEdit'>;
}) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: node.id,
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    left: `${node.position.x}px`,
    top: `${node.position.y}px`,
  };

  React.useEffect(() => {
    if (transform) {
      onMove?.({
        x: node.position.x + transform.x,
        y: node.position.y + transform.y,
      });
    }
  }, [transform, node.position.x, node.position.y, onMove]);

  // Determine node styling based on type and color
  const getNodeStyles = () => {
    const baseClasses = "absolute cursor-move select-none p-4 rounded-lg backdrop-blur-sm transition-all duration-300";
    const sizeClasses = node.size === 'sm' ? 'w-32 h-24' : node.size === 'lg' ? 'w-56 h-40' : 'w-44 h-32';

    // Type-specific styling
    let typeClasses = '';
    if (node.type === 'phase') {
      typeClasses = `border-l-4 border-${node.color || 'indigo'}-500 bg-gradient-to-br from-${node.color || 'indigo'}-900/80 to-${node.color || 'indigo'}-800/60`;
    } else if (node.type === 'milestone') {
      typeClasses = `border border-${node.color || 'purple'}-500/50 bg-gradient-to-br from-${node.color || 'purple'}-900/90 to-${node.color || 'purple'}-800/70 rounded-full flex flex-col justify-center items-center text-center`;
    } else {
      typeClasses = `border border-${node.color || 'cyan'}-500/30 bg-gradient-to-br from-${node.color || 'cyan'}-900/70 to-${node.color || 'cyan'}-800/50`;
    }

    // Active state styling
    const activeClasses = active
      ? `ring-2 ring-${node.color || 'cyan'}-400 shadow-lg shadow-${node.color || 'cyan'}-500/30`
      : 'shadow-md';

    return `${baseClasses} ${sizeClasses} ${typeClasses} ${activeClasses}`;
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onClick={() => {
        onClick();
      }}
      className={getNodeStyles()}
    >
      <div className={`flex items-center gap-2 mb-2 ${node.type === 'milestone' ? 'justify-center' : ''}`}>
        {node.icon && <span className="text-lg">{node.icon}</span>}
        <h3 className={`font-semibold text-white ${node.type === 'phase' ? 'text-lg' : ''}`}>{node.title}</h3>
      </div>
      {node.description && (
        <p className={`text-sm ${node.type === 'milestone' ? 'text-cyan-200 text-center' : 'text-gray-300'}`}>
          {node.description}
        </p>
      )}

      {/* Type indicator */}
      <div className="absolute top-1 right-1 text-xs text-gray-400 opacity-70">
        {node.type === 'phase' && translations.phase}
        {node.type === 'task' && translations.task}
        {node.type === 'milestone' && translations.milestone}
      </div>

      {/* Hover effect hint */}
      {active && (
        <div className="absolute bottom-1 right-1 text-xs text-cyan-300 animate-pulse">
          {translations.clickToEdit}
        </div>
      )}
    </div>
  );
};

const Connection = ({
  source,
  target,
  nodes,
}: {
  source: string;
  target: string;
  nodes: NodeData[];
}) => {
  const sourceNode = nodes.find(n => n.id === source);
  const targetNode = nodes.find(n => n.id === target);

  if (!sourceNode || !targetNode) return null;

  const sourceX = sourceNode.position.x + (sourceNode.size === 'sm' ? 64 : sourceNode.size === 'lg' ? 96 : 80);
  const sourceY = sourceNode.position.y + (sourceNode.size === 'sm' ? 12 : sourceNode.size === 'lg' ? 18 : 14);
  const targetX = targetNode.position.x;
  const targetY = targetNode.position.y + (targetNode.size === 'sm' ? 12 : targetNode.size === 'lg' ? 18 : 14);

  const length = Math.sqrt(Math.pow(targetX - sourceX, 2) + Math.pow(targetY - sourceY, 2));
  const angle = Math.atan2(targetY - sourceY, targetX - sourceX) * 180 / Math.PI;

  // Get color based on node type
  const getConnectionColor = () => {
    const sourceNodeColor = sourceNode?.color || 'cyan';
    return `var(--color-${sourceNodeColor}-500, #06b6d4)`;
  };

  return (
    <>
      {/* Main connection line */}
      <div
        className="absolute pointer-events-none"
        style={{
          left: `${sourceX}px`,
          top: `${sourceY}px`,
          width: `${length}px`,
          transform: `rotate(${angle}deg)`,
          transformOrigin: '0 0',
          height: '2px',
          background: `linear-gradient(90deg, ${getConnectionColor()}, rgba(203, 213, 225, 0.6))`,
          boxShadow: `0 0 8px ${getConnectionColor()}40`,
        }}
      />

      {/* Animated data flow effect */}
      <div
        className="absolute pointer-events-none overflow-hidden"
        style={{
          left: `${sourceX}px`,
          top: `${sourceY}px`,
          width: `${length}px`,
          transform: `rotate(${angle}deg)`,
          transformOrigin: '0 0',
          height: '2px',
        }}
      >
        <div
          className="h-full"
          style={{
            width: '30%',
            background: `linear-gradient(90deg, transparent, ${getConnectionColor()}, transparent)`,
            animation: 'flowAnimation 3s infinite linear',
          }}
        />
      </div>

      {/* Arrow at the end */}
      <div
        className="absolute pointer-events-none w-2 h-2 rotate-45 border-t-2 border-r-2"
        style={{
          left: `${targetX - 1}px`,
          top: `${targetY - 1}px`,
          borderColor: getConnectionColor(),
        }}
      />

      {/* Add a global style for the animation */}
      <style jsx global>{`
        @keyframes flowAnimation {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(300%); }
        }
      `}</style>
    </>
  );
};

const NodeDiagram: React.FC<NodeDiagramProps> = ({
  nodes,
  activeNode,
  onNodeClick,
  onNodeMove,
  onAddConnection,
  onDeleteNode,
  translations
}) => {
  const [connectionMode, setConnectionMode] = React.useState<string | null>(null);

  const handleNodeClick = (node: NodeData) => {
    if (connectionMode) {
      if (connectionMode === node.id) {
        setConnectionMode(null);
      } else {
        onAddConnection?.(connectionMode, node.id);
        setConnectionMode(null);
      }
    } else {
      onNodeClick(node);
    }
  };

  const handleBackgroundClick = () => {
    setConnectionMode(null);
    // Pass null to clear selection, but need to cast to NodeData to satisfy TypeScript
    onNodeClick?.(null as unknown as NodeData);
  };

  return (
    <div
      className="relative w-full h-full bg-gray-900 rounded-lg overflow-hidden"
      onClick={handleBackgroundClick}
    >
      {/* Grid background */}
      <div className="absolute inset-0 grid grid-cols-12 grid-rows-12 opacity-10">
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={`grid-col-${i}`} className="border-r border-cyan-500/30 h-full" />
        ))}
        {Array.from({ length: 12 }).map((_, i) => (
          <div key={`grid-row-${i}`} className="border-b border-cyan-500/30 w-full" />
        ))}
      </div>

      {/* Render connections first so nodes appear on top */}
      {nodes.flatMap(node =>
        (node.connections || []).map(connection => (
          <Connection
            key={`${node.id}-${connection}`}
            source={node.id}
            target={connection}
            nodes={nodes}
          />
        ))
      )}

      {/* Render nodes */}
      {nodes.map(node => (
        <Node
          key={node.id}
          node={node}
          active={activeNode === node.id}
          onClick={() => handleNodeClick(node)}
          onMove={(position) => onNodeMove?.(node.id, position)}
          translations={{
            phase: translations.phase,
            task: translations.task,
            milestone: translations.milestone,
            clickToEdit: translations.clickToEdit
          }}
        />
      ))}

      {/* Connection mode indicator */}
      {connectionMode && (
        <div className="fixed top-4 left-4 bg-indigo-900/80 text-cyan-300 px-4 py-2 rounded-md border border-cyan-500/50 backdrop-blur-sm shadow-lg">
          <div className="flex items-center gap-2">
            <span className="animate-pulse">⟁</span>
            <span>Connecting from: <span className="font-semibold">{nodes.find(n => n.id === connectionMode)?.title}</span></span>
          </div>
          <div className="text-xs text-cyan-400 mt-1">{translations.clickToConnect}</div>
        </div>
      )}

      {/* Controls */}
      <div className="fixed bottom-4 right-4 flex gap-2">
        <button
          onClick={() => {
            if (activeNode) {
              setConnectionMode(activeNode);
            }
          }}
          className="bg-gradient-to-r from-indigo-600 to-cyan-600 text-white px-4 py-2 rounded-md hover:from-indigo-700 hover:to-cyan-700 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          disabled={!activeNode}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 015.656 0l4 4a4 4 0 01-5.656 5.656l-1.102-1.101" />
          </svg>
          {translations.connect}
        </button>
        <button
          onClick={() => {
            if (activeNode && confirm(translations.deleteConfirm)) {
              onDeleteNode?.(activeNode);
            }
          }}
          className="bg-gradient-to-r from-red-600 to-pink-600 text-white px-4 py-2 rounded-md hover:from-red-700 hover:to-pink-700 transition-all shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          disabled={!activeNode}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          {translations.delete}
        </button>
      </div>
    </div>
  );
};

export default NodeDiagram;
