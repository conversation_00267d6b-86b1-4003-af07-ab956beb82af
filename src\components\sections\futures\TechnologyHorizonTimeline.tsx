'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface TechnologyHorizonTimelineProps {
  translations?: {
    title?: string;
    nearHorizon?: {
      title?: string;
      technologies?: {
        edgeAI?: {
          title?: string;
          description?: string;
        };
        hybridQuantum?: {
          title?: string;
          description?: string;
        };
        zeroTrust?: {
          title?: string;
          description?: string;
        };
      };
    };
    midHorizon?: {
      title?: string;
      technologies?: {
        neuromorphic?: {
          title?: string;
          description?: string;
        };
        digitalTwin?: {
          title?: string;
          description?: string;
        };
        ambientIntelligence?: {
          title?: string;
          description?: string;
        };
      };
    };
    farHorizon?: {
      title?: string;
      technologies?: {
        autonomousEnterprise?: {
          title?: string;
          description?: string;
        };
        syntheticData?: {
          title?: string;
          description?: string;
        };
        biologicalComputing?: {
          title?: string;
          description?: string;
        };
      };
    };
  };
}

export default function TechnologyHorizonTimeline({ translations = {} }: TechnologyHorizonTimelineProps) {
  const [activeHorizon, setActiveHorizon] = useState<string>('near');
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Icons for technologies
  const techIcons = {
    edgeAI: '🔄',
    hybridQuantum: '⚛️',
    zeroTrust: '🔒',
    neuromorphic: '🧠',
    digitalTwin: '🔄',
    ambientIntelligence: '🌐',
    autonomousEnterprise: '🤖',
    syntheticData: '📊',
    biologicalComputing: '🧬'
  };

  const horizonColors = {
    near: {
      bg: 'from-blue-600 to-cyan-500',
      border: 'border-blue-500',
      text: 'text-blue-400'
    },
    mid: {
      bg: 'from-purple-600 to-indigo-500',
      border: 'border-purple-500',
      text: 'text-purple-400'
    },
    far: {
      bg: 'from-fuchsia-600 to-pink-500',
      border: 'border-fuchsia-500',
      text: 'text-fuchsia-400'
    }
  };

  return (
    <section className="mb-12" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-12 text-center text-white"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
        >
          {translations.title}
        </motion.h2>

        {/* Timeline Navigation */}
        <div className="flex justify-center mb-8 sm:mb-12 md:mb-12">
          <div className="inline-flex bg-deep-space rounded-full p-1 border border-gray-700 w-full max-w-md sm:max-w-none sm:w-auto">
            <button
              className={`flex-1 sm:flex-none px-4 sm:px-6 md:px-6 py-3 sm:py-2 md:py-2 rounded-full text-xs sm:text-sm md:text-sm font-medium transition-all min-h-[44px] touch-manipulation ${
                activeHorizon === 'near'
                  ? `bg-gradient-to-r ${horizonColors.near.bg} text-white`
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setActiveHorizon('near')}
            >
              {translations.nearHorizon?.title}
            </button>
            <button
              className={`flex-1 sm:flex-none px-4 sm:px-6 md:px-6 py-3 sm:py-2 md:py-2 rounded-full text-xs sm:text-sm md:text-sm font-medium transition-all min-h-[44px] touch-manipulation ${
                activeHorizon === 'mid'
                  ? `bg-gradient-to-r ${horizonColors.mid.bg} text-white`
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setActiveHorizon('mid')}
            >
              {translations.midHorizon?.title}
            </button>
            <button
              className={`flex-1 sm:flex-none px-4 sm:px-6 md:px-6 py-3 sm:py-2 md:py-2 rounded-full text-xs sm:text-sm md:text-sm font-medium transition-all min-h-[44px] touch-manipulation ${
                activeHorizon === 'far'
                  ? `bg-gradient-to-r ${horizonColors.far.bg} text-white`
                  : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => setActiveHorizon('far')}
            >
              {translations.farHorizon?.title}
            </button>
          </div>
        </div>

        {/* Timeline Content */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6"
          initial={{ opacity: 0, y: 40 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, staggerChildren: 0.1 }}
        >
          {/* Near Horizon Technologies */}
          {activeHorizon === 'near' && (
            <>
              <TechCard
                title={translations.nearHorizon?.technologies?.edgeAI?.title || ''}
                description={translations.nearHorizon?.technologies?.edgeAI?.description || ''}
                icon={techIcons.edgeAI}
                color={horizonColors.near.border}
                bgGradient={horizonColors.near.bg}
                index={0}
              />
              <TechCard
                title={translations.nearHorizon?.technologies?.hybridQuantum?.title || ''}
                description={translations.nearHorizon?.technologies?.hybridQuantum?.description || ''}
                icon={techIcons.hybridQuantum}
                color={horizonColors.near.border}
                bgGradient={horizonColors.near.bg}
                index={1}
              />
              <TechCard
                title={translations.nearHorizon?.technologies?.zeroTrust?.title || ''}
                description={translations.nearHorizon?.technologies?.zeroTrust?.description || ''}
                icon={techIcons.zeroTrust}
                color={horizonColors.near.border}
                bgGradient={horizonColors.near.bg}
                index={2}
              />
            </>
          )}

          {/* Mid Horizon Technologies */}
          {activeHorizon === 'mid' && (
            <>
              <TechCard
                title={translations.midHorizon?.technologies?.neuromorphic?.title || ''}
                description={translations.midHorizon?.technologies?.neuromorphic?.description || ''}
                icon={techIcons.neuromorphic}
                color={horizonColors.mid.border}
                bgGradient={horizonColors.mid.bg}
                index={0}
              />
              <TechCard
                title={translations.midHorizon?.technologies?.digitalTwin?.title || ''}
                description={translations.midHorizon?.technologies?.digitalTwin?.description || ''}
                icon={techIcons.digitalTwin}
                color={horizonColors.mid.border}
                bgGradient={horizonColors.mid.bg}
                index={1}
              />
              <TechCard
                title={translations.midHorizon?.technologies?.ambientIntelligence?.title || ''}
                description={translations.midHorizon?.technologies?.ambientIntelligence?.description || ''}
                icon={techIcons.ambientIntelligence}
                color={horizonColors.mid.border}
                bgGradient={horizonColors.mid.bg}
                index={2}
              />
            </>
          )}

          {/* Far Horizon Technologies */}
          {activeHorizon === 'far' && (
            <>
              <TechCard
                title={translations.farHorizon?.technologies?.autonomousEnterprise?.title || ''}
                description={translations.farHorizon?.technologies?.autonomousEnterprise?.description || ''}
                icon={techIcons.autonomousEnterprise}
                color={horizonColors.far.border}
                bgGradient={horizonColors.far.bg}
                index={0}
              />
              <TechCard
                title={translations.farHorizon?.technologies?.syntheticData?.title || ''}
                description={translations.farHorizon?.technologies?.syntheticData?.description || ''}
                icon={techIcons.syntheticData}
                color={horizonColors.far.border}
                bgGradient={horizonColors.far.bg}
                index={1}
              />
              <TechCard
                title={translations.farHorizon?.technologies?.biologicalComputing?.title || ''}
                description={translations.farHorizon?.technologies?.biologicalComputing?.description || ''}
                icon={techIcons.biologicalComputing}
                color={horizonColors.far.border}
                bgGradient={horizonColors.far.bg}
                index={2}
              />
            </>
          )}
        </motion.div>
      </div>
    </section>
  );
}

interface TechCardProps {
  title: string;
  description: string;
  icon: string;
  color: string;
  bgGradient: string;
  index: number;
}

function TechCard({ title, description, icon, color, bgGradient, index }: TechCardProps) {
  return (
    <motion.div
      className={`bg-deep-space border ${color} rounded-xl p-4 sm:p-6 md:p-6 hover:shadow-lg hover:shadow-purple-500/10 transition-all touch-manipulation`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <div className={`w-10 h-10 sm:w-12 sm:h-12 md:w-12 md:h-12 rounded-full bg-gradient-to-r ${bgGradient} flex items-center justify-center text-xl sm:text-2xl md:text-2xl mb-3 sm:mb-4 md:mb-4`}>
        {icon}
      </div>
      <h3 className="text-lg sm:text-xl md:text-xl font-bold mb-2 text-white">{title}</h3>
      <p className="text-gray-400 text-sm sm:text-base md:text-base">{description}</p>
    </motion.div>
  );
}
