'use client';

import ProductsPage from './products/ProductsPage';

interface ProductManagementProps {
  translations?: {
    title?: string;
    subtitle?: string;
    exploreButton?: string;
    managementPhases?: {
      productDevelopment?: {
        title?: string;
        description?: string;
        processes?: {
          productFeasibility?: string;
          developmentMedical?: string;
          rAndD?: string;
          productRoadmap?: string;
        };
      };
      marketStrategy?: {
        title?: string;
        description?: string;
        processes?: {
          productPositioning?: string;
          competitorAnalysis?: string;
          licensingStrategy?: string;
          roadmapPlanning?: string;
          yearlyPlanning?: string;
        };
      };
      launchPreparation?: {
        title?: string;
        description?: string;
        processes?: {
          marketingStrategy?: string;
          launchPreparation?: string;
          communicationPlan?: string;
          organizationalChart?: string;
          changeManagement?: string;
        };
      };
      postMarket?: {
        title?: string;
        description?: string;
        processes?: {
          postMarketSurveillance?: string;
          roadmapReleases?: string;
          changeManagement?: string;
          communicationPlan?: string;
        };
      };
    };
    components?: {
      keyProcesses?: string;
      processDetailsTitle?: string;
      keyFeatures?: string;
      keyElements?: string;
      roadmapDevelopmentProcess?: string;
      roadmapDevelopmentSteps?: string[];
      strategicRoadmapFramework?: string;
      marketAnalysis?: {
        title?: string;
        items?: string[];
      };
      strategicPlanning?: {
        title?: string;
        items?: string[];
      };
      roadmapExecution?: {
        title?: string;
        releaseTimeline?: string;
        phasedImplementation?: string;
        milestoneTracking?: string;
        continuousFeedback?: string;
      };
      businessImpact?: {
        title?: string;
        impacts?: string[];
      };
      tryRoadmapTool?: string;
      roadmapToolDescription?: string;
      roadmapPlanning?: {
        title?: string;
        description?: string;
        elements?: {
          feasibility?: {
            title?: string;
            description?: string;
          };
          positioning?: {
            title?: string;
            description?: string;
          };
          releases?: {
            title?: string;
            description?: string;
          };
          planning?: {
            title?: string;
            description?: string;
          };
        };
      };
      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
      processDetails?: {
        closeButton?: string;
        implementationBenefits?: string;
        contentNotAvailable?: string;
        defaultFallbackDescription?: string;
        defaultBenefits?: string[];
        defaultFeatures?: string[];
      };
    };
  };
}

const ProductManagement: React.FC<ProductManagementProps> = ({ translations }) => {
  return <ProductsPage translations={translations} />;
};

export default ProductManagement;
