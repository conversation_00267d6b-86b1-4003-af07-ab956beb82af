'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface ModelTrainingTabProps {
  translations: {
    architecturePhases: {
      modelTraining: {
        title: string;
        description: string;
        icon: string;
        components: string[];
      };
    };
    componentDetails: Record<string, {
      description: string;
      features: string[];
      benefits: string[];
      icon: string;
      implementationTech: string[];
      technicalConsiderations: string[];
    }>;
    keyComponents: string;
    keyFeatures: string;
    keyBenefits: string;
  };
}

export default function ModelTrainingTab({ translations }: ModelTrainingTabProps) {
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1, rootMargin: '0px 0px -100px 0px' });

  const handleComponentClick = (component: string) => {
    setSelectedComponent(component === selectedComponent ? null : component);
  };

  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedComponent) {
        setSelectedComponent(null);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedComponent]);

  // Handle backdrop click to close component details
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setSelectedComponent(null);
    }
  };

  const phaseVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5
      }
    })
  };

  const currentPhase = translations.architecturePhases.modelTraining;
  const componentDetails = translations.componentDetails ?? {};

  return (
    <section ref={ref} className="relative w-full pt-6 pb-16 overflow-hidden">
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="bg-gray-900/60 backdrop-blur-sm rounded-lg border border-gray-800 p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row items-start gap-6">
            <div className="text-cyan-400 text-3xl flex-shrink-0">
              {currentPhase.icon}
            </div>

            <div className="flex-grow">
              <h3 className="text-2xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
                {currentPhase.title}
              </h3>
              <p className="text-gray-300 text-lg mb-6">
                {currentPhase.description}
              </p>

            <h4 className="text-xl font-semibold mb-4 text-cyan-400">
              {translations.keyComponents}
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {currentPhase.components.map((component, idx) => (
                <motion.div
                  key={component}
                  className={`bg-gray-800/50 border ${
                    selectedComponent === component
                      ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                      : 'border-purple-500/20'
                  } rounded-lg p-4 cursor-pointer transition-all hover:bg-gray-800/80 relative`}
                  custom={idx}
                  variants={phaseVariants}
                  initial="hidden"
                  animate={isIntersecting ? "visible" : "hidden"}
                  onClick={() => handleComponentClick(component)}
                  whileHover={{ scale: 1.02 }}
                >
                  <span className="text-white font-medium">{component}</span>
                </motion.div>
              ))}
            </div>

            {/* Component Details Section */}
            {selectedComponent && componentDetails[selectedComponent] && (
              <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
                onClick={handleBackdropClick}
              >
                <motion.div
                  className="mt-6 p-6 rounded-lg border border-white/20 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm max-w-4xl w-full mx-4 relative"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  onClick={(e) => e.stopPropagation()}
                  style={{
                    boxShadow: '0 0 20px rgba(99, 102, 241, 0.5), 0 0 40px rgba(99, 102, 241, 0.2), inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <button
                    onClick={() => setSelectedComponent(null)}
                    className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
                    aria-label="Close"
                  >
                    <X size={24} />
                  </button>
                <div className="flex items-start gap-4 mb-6">
                  <div
                    className="text-3xl bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center"
                    style={{
                      boxShadow: '0 0 10px rgba(6, 182, 212, 0.7), 0 0 20px rgba(168, 85, 247, 0.5), inset 0 0 5px rgba(255, 255, 255, 0.5)',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      textShadow: '0 0 5px rgba(255, 255, 255, 0.8)'
                    }}
                  >
                    <span style={{
                      filter: 'brightness(1.2) contrast(1.2)',
                      color: '#ffffff'
                    }}>{componentDetails[selectedComponent].icon}</span>
                  </div>
                  <div>
                    <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">{selectedComponent}</h3>
                    <p className="mb-6 text-gray-300">{componentDetails[selectedComponent].description}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                    style={{
                      boxShadow: '0 0 10px rgba(6, 182, 212, 0.3), inset 0 0 5px rgba(6, 182, 212, 0.1)',
                      background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(0, 0, 0, 0.7))'
                    }}>
                    <h4 className="font-semibold text-cyan-400 mb-2" style={{ textShadow: '0 0 5px rgba(6, 182, 212, 0.8)' }}>
                      {translations.keyFeatures}
                    </h4>
                    <ul className="space-y-2 text-gray-300">
                      {componentDetails[selectedComponent]?.features?.map((feature, index) => (
                        <li key={`feature-${index}`} className="flex items-start">
                          <span className="text-cyan-400 mr-2" style={{ textShadow: '0 0 3px rgba(6, 182, 212, 0.8)' }}>→</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                    style={{
                      boxShadow: '0 0 10px rgba(236, 72, 153, 0.3), inset 0 0 5px rgba(236, 72, 153, 0.1)',
                      background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(0, 0, 0, 0.7))'
                    }}>
                    <h4 className="font-semibold text-pink-400 mb-2" style={{ textShadow: '0 0 5px rgba(236, 72, 153, 0.8)' }}>
                      {translations.keyBenefits}
                    </h4>
                    <ul className="space-y-2 text-gray-300">
                      {componentDetails[selectedComponent]?.benefits?.map((benefit, index) => (
                        <li key={`benefit-${index}`} className="flex items-start">
                          <span className="text-pink-400 mr-2" style={{ textShadow: '0 0 3px rgba(236, 72, 153, 0.8)' }}>→</span>
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                </motion.div>
              </div>
            )}
          </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
