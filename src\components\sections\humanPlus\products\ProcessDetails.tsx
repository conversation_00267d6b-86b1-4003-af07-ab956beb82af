'use client';

import { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';

// This component now relies entirely on translations - no hardcoded fallbacks

interface ProcessDetailsProps {
  process: string;
  translations?: {
    components?: {
      processDetailsTitle?: string;
      keyFeatures?: string;
      keyElements?: string;
      roadmapDevelopmentProcess?: string;
      roadmapDevelopmentSteps?: string[];
      strategicRoadmapFramework?: string;
      marketAnalysis?: {
        title?: string;
        items?: string[];
      };
      strategicPlanning?: {
        title?: string;
        items?: string[];
      };
      roadmapExecution?: {
        title?: string;
        releaseTimeline?: string;
        phasedImplementation?: string;
        milestoneTracking?: string;
        continuousFeedback?: string;
      };
      businessImpact?: {
        title?: string;
        impacts?: string[];
      };
      tryRoadmapTool?: string;
      roadmapToolDescription?: string;
      processDetails?: {
        closeButton?: string;
        implementationBenefits?: string;
        contentNotAvailable?: string;
        defaultFallbackDescription?: string;
        defaultBenefits?: string[];
        defaultFeatures?: string[];
      };

      processDetailsContent?: {
        [key: string]: {
          description?: string;
          features?: string[];
        };
      };
    };
  };
  onClose: () => void;
}

export default function ProcessDetails({ process, translations, onClose }: ProcessDetailsProps) {
  // Add keyboard event listener for Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Render the modal using a portal to ensure it appears at the document root
  const modalContent = (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60]"
      onClick={handleBackdropClick}
    >
      <motion.div
        className="mt-6 p-6 rounded-lg border border-white/20 bg-gradient-to-br from-gray-900 to-black/60 backdrop-blur-sm max-w-4xl w-full mx-4 relative"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        onClick={(e) => e.stopPropagation()}
        style={{
          boxShadow: '0 0 20px rgba(99, 102, 241, 0.5), 0 0 40px rgba(99, 102, 241, 0.2), inset 0 0 15px rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
          aria-label={translations?.components?.processDetails?.closeButton || 'Close'}
        >
          <X size={24} />
        </button>

        <div className="flex items-start gap-4 mb-6">
          <div
            className="text-3xl bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full h-14 w-14 flex items-center justify-center"
            style={{
              boxShadow: '0 0 10px rgba(6, 182, 212, 0.7), 0 0 20px rgba(168, 85, 247, 0.5), inset 0 0 5px rgba(255, 255, 255, 0.5)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              textShadow: '0 0 5px rgba(255, 255, 255, 0.8)'
            }}
          >
            <span style={{
              filter: 'brightness(1.2) contrast(1.2)',
              color: '#ffffff'
            }}>📋</span>
          </div>
          <div>
            <h3 className="text-xl md:text-2xl font-semibold text-white mb-3">{process}</h3>
          </div>
        </div>

        <div className="space-y-6">
          {/* Get the process key from the selected component */}
          {(() => {
            // Convert the selected component to a camelCase key
            // Handle special cases first
            let processKey = process?.toLowerCase();

            // Handle specific mappings for special cases
            const specialMappings: Record<string, string> = {
              'yearly p&l planning': 'yearlyPlanning',
              'r&d': 'rAndD',
              'development - medical': 'developmentMedical',
              'development - cross-industry excellence': 'developmentMedical',
              'r&d innovation pipeline': 'rAndD',
              'strategic product roadmap': 'productRoadmap',
              'advanced product positioning': 'productPositioning',
              'comprehensive competitor intelligence': 'competitorAnalysis',
              'strategic licensing & partnership framework': 'licensingStrategy',
              'integrated business planning & valuation': 'roadmapPlanning',
              'dynamic p&l planning': 'yearlyPlanning',
              'integrated marketing strategy': 'marketingStrategy',
              'comprehensive launch preparation': 'launchPreparation',
              'strategic communication planning': 'communicationPlan',
              'organizational design & change management': 'organizationalChart',
              'advanced post-market surveillance': 'postMarketSurveillance',
              'dynamic roadmap evolution': 'roadmapReleases'
            };

            // Check if this is a Post-Market section process that needs special handling
            const postMarketSpecialMappings: Record<string, string> = {
              'change management': 'postMarketChangeManagement',
              'continuous change management': 'postMarketChangeManagement',
              'communication plan': 'postMarketCommunicationPlan',
              'strategic communication optimization': 'postMarketCommunicationPlan'
            };

            // Determine if we're in Post-Market context by checking if the process is one of the Post-Market processes
            const isPostMarketContext = ['Advanced Post-Market Surveillance', 'Dynamic Roadmap Evolution', 'Continuous Change Management', 'Strategic Communication Optimization'].includes(process);

            if (isPostMarketContext && postMarketSpecialMappings[processKey]) {
              processKey = postMarketSpecialMappings[processKey];
            } else if (specialMappings[processKey]) {
              processKey = specialMappings[processKey];
            } else {
              // Standard camelCase conversion
              processKey = processKey
                .replace(/[&\-]/g, ' ') // Replace & and - with spaces
                .replace(/\s+/g, ' ') // Normalize spaces
                .trim()
                .split(' ')
                .map((word, index) =>
                  index === 0
                    ? word.toLowerCase()
                    : word.charAt(0).toUpperCase() + word.slice(1)
                )
                .join('')
                .replace(/[^a-zA-Z0-9]/g, ''); // Remove any remaining special characters
            }

            // Get the process details from translations or use fallback
            const translatedDetails = translations?.components?.processDetailsContent?.[processKey];

            // Determine which description and features to use - rely entirely on translations
            const description = translatedDetails?.description ||
              translations?.components?.processDetails?.defaultFallbackDescription?.replace('{process}', process) ||
              translations?.components?.processDetails?.contentNotAvailable ||
              '';

            const features = translatedDetails?.features ||
              translations?.components?.processDetails?.defaultFeatures?.map((feature: string) =>
                feature.replace('{process}', process)
              ) || [];

            return (
              <>
                <p className="mb-6 text-gray-300">{description}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                    style={{
                      boxShadow: '0 0 10px rgba(6, 182, 212, 0.3), inset 0 0 5px rgba(6, 182, 212, 0.1)',
                      background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(0, 0, 0, 0.7))'
                    }}>
                    <h4 className="font-semibold text-cyan-400 mb-2" style={{ textShadow: '0 0 5px rgba(6, 182, 212, 0.8)' }}>
                      {translations?.components?.keyFeatures || 'Key Features'}
                    </h4>
                    <ul className="space-y-2 text-gray-300">
                      {features.map((feature: string, index: number) => (
                        <li key={`feature-${index}`} className="flex items-start">
                          <span className="text-cyan-400 mr-2" style={{ textShadow: '0 0 3px rgba(6, 182, 212, 0.8)' }}>→</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="p-4 bg-black/50 rounded-lg border border-white/10"
                    style={{
                      boxShadow: '0 0 10px rgba(236, 72, 153, 0.3), inset 0 0 5px rgba(236, 72, 153, 0.1)',
                      background: 'linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(0, 0, 0, 0.7))'
                    }}>
                    <h4 className="font-semibold text-pink-400 mb-2" style={{ textShadow: '0 0 5px rgba(236, 72, 153, 0.8)' }}>
                      {translations?.components?.processDetails?.implementationBenefits || 'Implementation Benefits'}
                    </h4>
                    <ul className="space-y-2 text-gray-300">
                      {(translations?.components?.processDetails?.defaultBenefits || []).map((benefit, index) => (
                        <li key={`benefit-${index}`} className="flex items-start">
                          <span className="text-pink-400 mr-2" style={{ textShadow: '0 0 3px rgba(236, 72, 153, 0.8)' }}>→</span>
                          <span>{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </>
            );
          })()}
        </div>
      </motion.div>
    </div>
  );

  // Use createPortal to render the modal at the document root
  return typeof window !== 'undefined'
    ? createPortal(modalContent, document.body)
    : null;
}
