import { getServerTranslations } from '@/lib/i18n';
import MLArchitecturePageContent from '@/components/sections/ml/MLArchitecturePage';

export default async function ArchitecturesPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const translations = {
    // Interactive explorer translations
    title: t('services.mlArchitecture.pageContent.title'),
    subtitle: t('services.mlArchitecture.pageContent.subtitle'),
    exploreButton: t('services.mlArchitecture.pageContent.launchExplorer'),
    phases: {
      dataIngestion: t('services.mlArchitecture.phases.dataIngestion'),
      modelTraining: t('services.mlArchitecture.phases.modelTraining'),
      deployment: t('services.mlArchitecture.phases.deployment'),
      monitoring: t('services.mlArchitecture.phases.monitoring'),
    },
    viewModes: {
      conceptual: t('services.mlArchitecture.viewModes.conceptual'),
      implementation: t('services.mlArchitecture.viewModes.implementation'),
    },
    buttons: {
      zoomIn: t('services.mlArchitecture.buttons.zoomIn'),
      zoomOut: t('services.mlArchitecture.buttons.zoomOut'),
      fullscreen: t('services.mlArchitecture.buttons.fullscreen'),
      close: t('services.mlArchitecture.buttons.close'),
      runData: t('services.mlArchitecture.buttons.runData'),
      reset: t('services.mlArchitecture.buttons.reset'),
    },
    // Architecture phases data
    architecturePhases: {
      dataIngestion: {
        title: t('services.mlArchitecture.pageContent.architecturePhases.dataIngestion.title'),
        description: t('services.mlArchitecture.pageContent.architecturePhases.dataIngestion.description'),
        icon: t('services.mlArchitecture.pageContent.architecturePhases.dataIngestion.icon'),
        components: [
          "Data Collector",
          "Data Preprocessor",
          "Feature Selector",
          "Data Quality Monitor"
        ]
      },
      modelTraining: {
        title: t('services.mlArchitecture.pageContent.architecturePhases.modelTraining.title'),
        description: t('services.mlArchitecture.pageContent.architecturePhases.modelTraining.description'),
        icon: t('services.mlArchitecture.pageContent.architecturePhases.modelTraining.icon'),
        components: [
          "Model Builder",
          "Model Trainer",
          "Model Evaluator",
          "Automatic Model Selector",
          "Hypothesis Executor"
        ]
      },
      deployment: {
        title: t('services.mlArchitecture.pageContent.architecturePhases.deployment.title'),
        description: t('services.mlArchitecture.pageContent.architecturePhases.deployment.description'),
        icon: t('services.mlArchitecture.pageContent.architecturePhases.deployment.icon'),
        components: [
          "Model Deployer",
          "Model Predictor",
          "Kubernetes Cluster",
          "Forecast Service"
        ]
      },
      monitoring: {
        title: t('services.mlArchitecture.pageContent.architecturePhases.monitoring.title'),
        description: t('services.mlArchitecture.pageContent.architecturePhases.monitoring.description'),
        icon: t('services.mlArchitecture.pageContent.architecturePhases.monitoring.icon'),
        components: [
          "Predictions Monitor",
          "Alert Processor",
          "Notification Service",
          "Retraining Trigger"
        ]
      }
    },
    // Component details
    componentDetails: {
      'Data Collector': t('services.mlArchitecture.pageContent.componentDetails.dataCollector', { returnObjects: true }),
      'Data Preprocessor': t('services.mlArchitecture.pageContent.componentDetails.dataPreprocessor', { returnObjects: true }),
      'Feature Selector': t('services.mlArchitecture.pageContent.componentDetails.featureSelector', { returnObjects: true }),
      'Data Quality Monitor': t('services.mlArchitecture.pageContent.componentDetails.dataQualityMonitor', { returnObjects: true }),
      'Model Builder': t('services.mlArchitecture.pageContent.componentDetails.modelBuilder', { returnObjects: true }),
      'Model Trainer': t('services.mlArchitecture.pageContent.componentDetails.modelTrainer', { returnObjects: true }),
      'Model Evaluator': t('services.mlArchitecture.pageContent.componentDetails.modelEvaluator', { returnObjects: true }),
      'Automatic Model Selector': t('services.mlArchitecture.pageContent.componentDetails.automaticModelSelector', { returnObjects: true }),
      'Hypothesis Executor': t('services.mlArchitecture.pageContent.componentDetails.hypothesisExecutor', { returnObjects: true }),
      'Model Deployer': t('services.mlArchitecture.pageContent.componentDetails.modelDeployer', { returnObjects: true }),
      'Model Predictor': t('services.mlArchitecture.pageContent.componentDetails.modelPredictor', { returnObjects: true }),
      'Kubernetes Cluster': t('services.mlArchitecture.pageContent.componentDetails.kubernetesCluster', { returnObjects: true }),
      'Forecast Service': t('services.mlArchitecture.pageContent.componentDetails.forecastService', { returnObjects: true }),
      'Predictions Monitor': t('services.mlArchitecture.pageContent.componentDetails.predictionsMonitor', { returnObjects: true }),
      'Alert Processor': t('services.mlArchitecture.pageContent.componentDetails.alertProcessor', { returnObjects: true }),
      'Notification Service': t('services.mlArchitecture.pageContent.componentDetails.notificationService', { returnObjects: true }),
      'Retraining Trigger': t('services.mlArchitecture.pageContent.componentDetails.retrainingTrigger', { returnObjects: true })
    },
    // Additional translation keys
    keyComponents: t('mlArchitecture.pageContent.keyComponents'),
    keyFeatures: t('mlArchitecture.pageContent.keyFeatures'),
    keyBenefits: t('mlArchitecture.pageContent.keyBenefits'),
    learnMore: t('services.mlArchitecture.learnMore') || 'Learn More',
    benefits: t('services.mlArchitecture.benefits') || 'Benefits',
    components: [],
    implementationTech: t('services.mlArchitecture.implementationTech') || 'Implementation Technology',
    technicalConsiderations: t('services.mlArchitecture.technicalConsiderations') || 'Technical Considerations',
    componentWorkflow: t('services.mlArchitecture.componentWorkflow') || 'Component Workflow',
    implementationArchitecture: t('services.mlArchitecture.implementationArchitecture') || 'Implementation Architecture',
    comingSoon: t('services.mlArchitecture.comingSoon') || 'Coming Soon',
    // Features section
    features: t('mlArchitecture.pageContent.features', { returnObjects: true }) as {
      title: string;
      items: Array<{
        icon: string;
        title: string;
        description: string;
      }>;
    },
    // CTA section
    cta: t('mlArchitecture.pageContent.cta', { returnObjects: true }) as {
      title: string;
      description: string;
      exploreButton: string;
      contactButton: string;
    }
  };

  return (
    <MLArchitecturePageContent
      translations={{
        ...translations,
        componentDetails: Object.entries(translations.componentDetails).reduce((acc, [key, value]) => {
          // Ensure each component has all required properties
          const componentData = value as {
            description?: string;
            features?: string[];
            benefits?: string[];
            icon?: string;
            implementationTech?: string[];
            technicalConsiderations?: string[];
          };

          acc[key] = {
            description: componentData?.description || '',
            features: componentData?.features || [],
            benefits: componentData?.benefits || [],
            icon: componentData?.icon || '',
            implementationTech: componentData?.implementationTech || [],
            technicalConsiderations: componentData?.technicalConsiderations || []
          };
          return acc;
        }, {} as Record<string, {
          description: string;
          features: string[];
          benefits: string[];
          icon: string;
          implementationTech: string[];
          technicalConsiderations: string[];
        }>)
      }}
      locale={resolvedParams.locale}
    />
  );
}
