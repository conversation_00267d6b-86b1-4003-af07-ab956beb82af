'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ParticleBackground from '@/components/animations/ParticleBackground';
import { Filter, Eye, Bar<PERSON>hart3, Clock, Target, Layers } from 'lucide-react';

interface TimeHorizonData {
  relevanceScore: number;
  growthRate: string;
  description: string;
  predictedGrowth: string;
}

interface Technology {
  name: string;
  description: string;
  maturity: 'emerging' | 'growing' | 'maturing';
  category: string;
  growthRate: string;
  relevanceScore: number;
  industryImpact: Record<string, string>;
  businessApplications?: string[];
  predictedGrowth?: string;
  keyPlayers?: string[];
  timeHorizonData?: {
    '6months'?: TimeHorizonData;
    '1year'?: TimeHorizonData;
    '5years'?: TimeHorizonData;
  };
}

interface PulseSectionProps {
  translations?: {
    title?: string;
    subtitle?: string;
    timeHorizon?: {
      title?: string;
      options?: {
        sixMonths?: {
          label?: string;
          description?: string;
        };
        oneYear?: {
          label?: string;
          description?: string;
        };
        fiveYears?: {
          label?: string;
          description?: string;
        };
      };
    };
    maturityLevel?: {
      title?: string;
      emerging?: string;
      growing?: string;
      maturing?: string;
    };
    businessImpact?: {
      title?: string;
      relevanceScore?: string;
      highImpactFor?: string;
    };
    industries?: {
      lifeInsurance?: string;
      techServices?: string;
      manufacturing?: string;
    };
    impactLevels?: {
      high?: string;
      veryHigh?: string;
      medium?: string;
      low?: string;
    };
    contentHeader?: {
      title?: string;
      matchingFilters?: string;
    };
    labels?: {
      growth?: string;
      maturity?: string;
      relevanceScore?: string;
      explore?: string;
      businessApplications?: string;
      keyPlayers?: string;
    };
    noResults?: {
      message?: string;
      resetButton?: string;
    };
    reportCta?: {
      title?: string;
      description?: string;
      button?: string;
    };
    technologies?: {
      digitalHealthUnderwriting?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      parametricLifeProducts?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      federatedLearning?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      causalAiSystems?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      quantumSafeCryptography?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      neuralProcessAutomation?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      digitalTwinEcosystems?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
    };
  };
}

const PulseSection: React.FC<PulseSectionProps> = ({ translations }) => {
  // State management for the dashboard
  const [selectedIndustry, setSelectedIndustry] = useState<string>('lifeInsurance');
  const [timeHorizon, setTimeHorizon] = useState<string>('6months');
  const [maturityFilters, setMaturityFilters] = useState<string[]>(['emerging', 'growing', 'maturing']);
  const [selectedTech, setSelectedTech] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Define maturity level colors consistent with design system
  const maturityColors = {
    emerging: '#10b981', // green
    growing: '#f59e0b', // amber
    maturing: '#3b82f6', // blue
  };

  // Technology data from translations
  const sampleTechnologies: Technology[] = [
    {
      name: translations?.technologies?.digitalHealthUnderwriting?.name || 'Digital Health Underwriting',
      description: translations?.technologies?.digitalHealthUnderwriting?.description || 'AI-powered health assessment using wearables and lifestyle data',
      maturity: 'emerging',
      category: translations?.technologies?.digitalHealthUnderwriting?.category || 'AI/ML',
      growthRate: translations?.technologies?.digitalHealthUnderwriting?.growthRate || '78%',
      relevanceScore: 95,
      industryImpact: { lifeInsurance: 'Very High', techServices: 'Medium', manufacturing: 'Low' },
      businessApplications: translations?.technologies?.digitalHealthUnderwriting?.businessApplications || ['Risk assessment automation', 'Real-time health monitoring', 'Personalized premium pricing'],
      predictedGrowth: translations?.technologies?.digitalHealthUnderwriting?.predictedGrowth || '89% by 2025',
      keyPlayers: translations?.technologies?.digitalHealthUnderwriting?.keyPlayers || ['John Hancock', 'Vitality', 'Oscar Health'],
      timeHorizonData: translations?.technologies?.digitalHealthUnderwriting?.timeHorizonData
    },
    {
      name: translations?.technologies?.parametricLifeProducts?.name || 'Parametric Life Products',
      description: translations?.technologies?.parametricLifeProducts?.description || 'Smart contracts trigger automatic payouts based on predefined parameters',
      maturity: 'growing',
      category: translations?.technologies?.parametricLifeProducts?.category || 'Blockchain',
      growthRate: translations?.technologies?.parametricLifeProducts?.growthRate || '89%',
      relevanceScore: 88,
      industryImpact: { lifeInsurance: 'High', techServices: 'Medium', manufacturing: 'Low' },
      businessApplications: translations?.technologies?.parametricLifeProducts?.businessApplications || ['Instant claim processing', 'Reduced fraud', 'Lower operational costs'],
      predictedGrowth: translations?.technologies?.parametricLifeProducts?.predictedGrowth || '156% by 2025',
      keyPlayers: translations?.technologies?.parametricLifeProducts?.keyPlayers || ['Etherisc', 'Nexus Mutual', 'InsurAce'],
      timeHorizonData: translations?.technologies?.parametricLifeProducts?.timeHorizonData
    },
    {
      name: translations?.technologies?.federatedLearning?.name || 'Federated Learning',
      description: translations?.technologies?.federatedLearning?.description || 'Privacy-preserving collaborative machine learning across institutions',
      maturity: 'emerging',
      category: translations?.technologies?.federatedLearning?.category || 'AI/ML',
      growthRate: translations?.technologies?.federatedLearning?.growthRate || '92%',
      relevanceScore: 82,
      industryImpact: { lifeInsurance: 'High', techServices: 'High', manufacturing: 'Medium' },
      businessApplications: translations?.technologies?.federatedLearning?.businessApplications || ['Cross-industry risk models', 'Privacy-compliant data sharing', 'Enhanced fraud detection'],
      predictedGrowth: translations?.technologies?.federatedLearning?.predictedGrowth || '134% by 2025',
      keyPlayers: translations?.technologies?.federatedLearning?.keyPlayers || ['Google', 'IBM', 'NVIDIA'],
      timeHorizonData: translations?.technologies?.federatedLearning?.timeHorizonData
    },
    {
      name: translations?.technologies?.causalAiSystems?.name || 'Causal AI Systems',
      description: translations?.technologies?.causalAiSystems?.description || 'AI that understands cause-effect relationships for reliable predictions',
      maturity: 'emerging',
      category: translations?.technologies?.causalAiSystems?.category || 'AI/ML',
      growthRate: translations?.technologies?.causalAiSystems?.growthRate || '67%',
      relevanceScore: 79,
      industryImpact: { lifeInsurance: 'High', techServices: 'Medium', manufacturing: 'Medium' },
      businessApplications: translations?.technologies?.causalAiSystems?.businessApplications || ['Mortality prediction', 'Policy optimization', 'Risk factor analysis'],
      predictedGrowth: translations?.technologies?.causalAiSystems?.predictedGrowth || '98% by 2025',
      keyPlayers: translations?.technologies?.causalAiSystems?.keyPlayers || ['Causality', 'Microsoft', 'Amazon'],
      timeHorizonData: translations?.technologies?.causalAiSystems?.timeHorizonData
    },
    {
      name: translations?.technologies?.quantumSafeCryptography?.name || 'Quantum-Safe Cryptography',
      description: translations?.technologies?.quantumSafeCryptography?.description || 'Post-quantum security algorithms protecting against future threats',
      maturity: 'maturing',
      category: translations?.technologies?.quantumSafeCryptography?.category || 'Security',
      growthRate: translations?.technologies?.quantumSafeCryptography?.growthRate || '45%',
      relevanceScore: 75,
      industryImpact: { lifeInsurance: 'High', techServices: 'Very High', manufacturing: 'Medium' },
      businessApplications: translations?.technologies?.quantumSafeCryptography?.businessApplications || ['Data protection', 'Secure communications', 'Future-proof encryption'],
      predictedGrowth: translations?.technologies?.quantumSafeCryptography?.predictedGrowth || '67% by 2025',
      keyPlayers: translations?.technologies?.quantumSafeCryptography?.keyPlayers || ['IBM', 'Google', 'NIST'],
      timeHorizonData: translations?.technologies?.quantumSafeCryptography?.timeHorizonData
    },
    {
      name: translations?.technologies?.neuralProcessAutomation?.name || 'Neural Process Automation',
      description: translations?.technologies?.neuralProcessAutomation?.description || 'AI learns and automates complex business processes automatically',
      maturity: 'growing',
      category: translations?.technologies?.neuralProcessAutomation?.category || 'AI/ML',
      growthRate: translations?.technologies?.neuralProcessAutomation?.growthRate || '73%',
      relevanceScore: 85,
      industryImpact: { lifeInsurance: 'Very High', techServices: 'High', manufacturing: 'High' },
      businessApplications: translations?.technologies?.neuralProcessAutomation?.businessApplications || ['Claims processing', 'Customer service', 'Policy administration'],
      predictedGrowth: translations?.technologies?.neuralProcessAutomation?.predictedGrowth || '112% by 2025',
      keyPlayers: translations?.technologies?.neuralProcessAutomation?.keyPlayers || ['UiPath', 'Automation Anywhere', 'Blue Prism'],
      timeHorizonData: translations?.technologies?.neuralProcessAutomation?.timeHorizonData
    },
    {
      name: translations?.technologies?.digitalTwinEcosystems?.name || 'Digital Twin Ecosystems',
      description: translations?.technologies?.digitalTwinEcosystems?.description || 'Interconnected digital replicas for comprehensive optimization',
      maturity: 'maturing',
      category: translations?.technologies?.digitalTwinEcosystems?.category || 'IoT',
      growthRate: translations?.technologies?.digitalTwinEcosystems?.growthRate || '58%',
      relevanceScore: 71,
      industryImpact: { lifeInsurance: 'Medium', techServices: 'High', manufacturing: 'Very High' },
      businessApplications: translations?.technologies?.digitalTwinEcosystems?.businessApplications || ['Customer behavior modeling', 'Product simulation', 'Risk scenario testing'],
      predictedGrowth: translations?.technologies?.digitalTwinEcosystems?.predictedGrowth || '87% by 2025',
      keyPlayers: translations?.technologies?.digitalTwinEcosystems?.keyPlayers || ['Siemens', 'GE Digital', 'Microsoft'],
      timeHorizonData: translations?.technologies?.digitalTwinEcosystems?.timeHorizonData
    }
  ];

  // Apply time horizon data to technologies
  const applyTimeHorizonData = (tech: Technology): Technology => {
    const horizonKey = timeHorizon as '6months' | '1year' | '5years';
    const horizonData = tech.timeHorizonData?.[horizonKey];

    if (horizonData) {
      return {
        ...tech,
        relevanceScore: horizonData.relevanceScore,
        growthRate: horizonData.growthRate,
        description: horizonData.description,
        predictedGrowth: horizonData.predictedGrowth
      };
    }

    return tech;
  };

  // Get all technologies with time horizon data applied (for counts and filtering)
  const getTechnologiesWithTimeHorizonData = useMemo(() => {
    return sampleTechnologies.map(applyTimeHorizonData);
  }, [timeHorizon, translations]); // Re-calculate when timeHorizon or translations change

  // Calculate maturity level counts dynamically based on time horizon and current filters
  const getMaturityCounts = useMemo(() => {
    const counts = {
      emerging: 0,
      growing: 0,
      maturing: 0
    };

    // Define relevance thresholds based on time horizon to make the effect more visible
    const relevanceThreshold = timeHorizon === '6months' ? 70 : timeHorizon === '1year' ? 60 : 50;

    // Count all technologies with time horizon data applied, but respect industry filter and relevance threshold
    const technologiesForCounting = getTechnologiesWithTimeHorizonData.filter(tech => {
      // Apply same industry filter as main filtering
      const highImpact = translations?.impactLevels?.high || 'High';
      const veryHighImpact = translations?.impactLevels?.veryHigh || 'Very High';
      const industryMatch = !selectedIndustry ||
        tech.industryImpact[selectedIndustry] === highImpact ||
        tech.industryImpact[selectedIndustry] === veryHighImpact;

      // Apply relevance threshold based on time horizon
      const relevanceMatch = tech.relevanceScore >= relevanceThreshold;

      return industryMatch && relevanceMatch;
    });

    technologiesForCounting.forEach(tech => {
      if (counts.hasOwnProperty(tech.maturity)) {
        counts[tech.maturity as keyof typeof counts]++;
      }
    });

    return counts;
  }, [getTechnologiesWithTimeHorizonData, selectedIndustry, translations, timeHorizon]);

  // Filter technologies based on current selections
  const getFilteredTechnologies = useMemo(() => {
    // Define relevance thresholds based on time horizon to make the effect more visible
    const relevanceThreshold = timeHorizon === '6months' ? 70 : timeHorizon === '1year' ? 60 : 50;

    return getTechnologiesWithTimeHorizonData
      .filter(tech => {
        // Industry filter
        const highImpact = translations?.impactLevels?.high || 'High';
        const veryHighImpact = translations?.impactLevels?.veryHigh || 'Very High';
        const industryMatch = !selectedIndustry ||
          tech.industryImpact[selectedIndustry] === highImpact ||
          tech.industryImpact[selectedIndustry] === veryHighImpact;

        // Maturity filter
        const maturityMatch = maturityFilters.includes(tech.maturity);

        // Relevance threshold based on time horizon
        const relevanceMatch = tech.relevanceScore >= relevanceThreshold;

        return industryMatch && maturityMatch && relevanceMatch;
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore); // Sort by relevance score for the selected time horizon
  }, [getTechnologiesWithTimeHorizonData, selectedIndustry, maturityFilters, translations, timeHorizon]);

  // Calculate dynamic business impact based on filtered technologies
  const calculateBusinessImpact = useMemo(() => {
    if (getFilteredTechnologies.length === 0) return 0;

    const averageRelevance = getFilteredTechnologies.reduce((sum: number, tech: Technology) => sum + tech.relevanceScore, 0) / getFilteredTechnologies.length;
    return Math.round(averageRelevance);
  }, [getFilteredTechnologies]);

  // Handle technology selection
  const handleTechSelect = (techName: string) => {
    setSelectedTech(selectedTech === techName ? null : techName);
  };

  // Handle maturity filter toggle
  const toggleMaturityFilter = (maturity: string) => {
    if (maturityFilters.includes(maturity)) {
      setMaturityFilters(maturityFilters.filter(m => m !== maturity));
    } else {
      setMaturityFilters([...maturityFilters, maturity]);
    }
  };

  // Industry options
  const industryOptions = [
    { key: 'lifeInsurance', label: translations?.industries?.lifeInsurance || 'Life Insurance', icon: '🏥' },
    { key: 'techServices', label: translations?.industries?.techServices || 'Tech Services', icon: '💻' },
    { key: 'manufacturing', label: translations?.industries?.manufacturing || 'Manufacturing', icon: '🏭' }
  ];

  // Time horizon options
  const timeHorizonOptions = [
    {
      key: '6months',
      label: translations?.timeHorizon?.options?.sixMonths?.label || '6 Months',
      description: translations?.timeHorizon?.options?.sixMonths?.description || 'Immediate impact'
    },
    {
      key: '1year',
      label: translations?.timeHorizon?.options?.oneYear?.label || '1 Year',
      description: translations?.timeHorizon?.options?.oneYear?.description || 'Short-term adoption'
    },
    {
      key: '5years',
      label: translations?.timeHorizon?.options?.fiveYears?.label || '5 Years',
      description: translations?.timeHorizon?.options?.fiveYears?.description || 'Long-term transformation'
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };



  return (
    <div className="bg-[#121826] text-white font-['Montserrat',sans-serif]">
      {/* Background particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <ParticleBackground
            color="#4FC3F7"
            secondaryColor="#7C4DFF"
            density="medium"
            speed="slow"
          />
        </div>
      </div>

      {/* Header Section */}
      <section ref={ref} className="relative" id="pulse-section" style={{ marginTop: 'var(--nav-sticky-top)' }}>
        <div className="bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-sm border-b border-gray-700/50">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              {/* Title and Subtitle */}
              <div className="flex-1">
                <h1 className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {translations?.title || 'Technology Pulse'}
                </h1>
                <p className="text-gray-300 text-lg">
                  {translations?.subtitle || 'Real-time emerging tech tracker'}
                </p>
              </div>

              {/* Industry Filter Pills */}
              <div className="flex flex-wrap gap-3">
                {industryOptions.map((industry) => (
                  <button
                    key={industry.key}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                      selectedIndustry === industry.key
                        ? 'bg-[#4FC3F7] text-white shadow-lg shadow-[#4FC3F7]/25'
                        : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50'
                    }`}
                    onClick={() => setSelectedIndustry(industry.key)}
                  >
                    <span className="mr-2">{industry.icon}</span>
                    {industry.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard Layout */}
        <div className="container mx-auto px-4 py-8">
          <div className="flex gap-8">
            {/* Left Sidebar - 280px width */}
            <motion.div
              className="w-[280px] flex-shrink-0"
              initial={{ opacity: 0, x: -20 }}
              animate={isIntersecting ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="sticky top-24 space-y-6">
                {/* Time Horizon Selector */}
                <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Clock className="w-4 h-4 text-[#4FC3F7]" />
                    {translations?.timeHorizon?.title || 'Time Horizon'}
                  </h3>
                  <div className="bg-gray-800/50 rounded-lg p-1 flex">
                    {timeHorizonOptions.map((option) => (
                      <motion.button
                        key={option.key}
                        className={`flex-1 px-3 py-2 rounded-md text-xs font-medium transition-all ${
                          timeHorizon === option.key
                            ? 'bg-[#4FC3F7] text-white shadow-lg shadow-[#4FC3F7]/25'
                            : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                        }`}
                        onClick={() => setTimeHorizon(option.key)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        animate={timeHorizon === option.key ? {
                          boxShadow: '0 4px 12px rgba(79, 195, 247, 0.4)'
                        } : {}}
                        transition={{ duration: 0.2 }}
                      >
                        {option.label}
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Maturity Level Filters */}
                <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Filter className="w-4 h-4 text-[#4FC3F7]" />
                    {translations?.maturityLevel?.title || 'Maturity Level'}
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(maturityColors).map(([key, color]) => (
                      <label key={key} className="flex items-center gap-3 cursor-pointer group">
                        <input
                          type="checkbox"
                          checked={maturityFilters.includes(key)}
                          onChange={() => toggleMaturityFilter(key)}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-all ${
                          maturityFilters.includes(key)
                            ? 'border-transparent'
                            : 'border-gray-600 group-hover:border-gray-500'
                        }`} style={{ backgroundColor: maturityFilters.includes(key) ? color : 'transparent' }}>
                          {maturityFilters.includes(key) && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </div>
                        <span className="text-gray-300 text-sm capitalize group-hover:text-white transition-colors">
                          {key}
                        </span>
                        <span className="text-xs text-gray-500 ml-auto">
                          ({getMaturityCounts[key as keyof typeof getMaturityCounts]})
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Business Impact Meter */}
                <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Target className="w-4 h-4 text-[#4FC3F7]" />
                    {translations?.businessImpact?.title || 'Business Impact'}
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-300">{translations?.businessImpact?.relevanceScore || 'Relevance Score'}</span>
                      <span className="text-lg font-bold text-[#4FC3F7]">{calculateBusinessImpact}%</span>
                    </div>
                    <div className="w-full h-3 bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] rounded-full transition-all duration-500"
                        style={{ width: `${calculateBusinessImpact}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-400">
                      {translations?.businessImpact?.highImpactFor || 'High impact for'} {industryOptions.find(i => i.key === selectedIndustry)?.label}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Main Content Area */}
            <motion.div
              className="flex-1"
              initial={{ opacity: 0 }}
              animate={isIntersecting ? { opacity: 1 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {/* Content Header */}
              <div className="bg-gradient-to-r from-gray-900/70 to-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 mb-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-2xl font-bold text-white">
                    {translations?.contentHeader?.title || 'Trending Technologies'}
                  </h2>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-gray-300">
                      {getFilteredTechnologies.length} {translations?.contentHeader?.matchingFilters || 'technologies match your filters'}
                      <span className="ml-2 text-xs text-[#4FC3F7]">({timeHorizon})</span>
                    </span>
                    <div className="flex items-center gap-2">
                      <button
                        className={`p-2 rounded-lg transition-all ${
                          viewMode === 'grid'
                            ? 'bg-[#4FC3F7] text-white'
                            : 'bg-gray-700 text-gray-400 hover:text-white'
                        }`}
                        onClick={() => setViewMode('grid')}
                      >
                        <Layers className="w-4 h-4" />
                      </button>
                      <button
                        className={`p-2 rounded-lg transition-all ${
                          viewMode === 'list'
                            ? 'bg-[#4FC3F7] text-white'
                            : 'bg-gray-700 text-gray-400 hover:text-white'
                        }`}
                        onClick={() => setViewMode('list')}
                      >
                        <BarChart3 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>



              {/* Technology Cards Grid */}
              <motion.div
                key={timeHorizon} // Re-animate when time horizon changes
                variants={containerVariants}
                initial="hidden"
                animate={isIntersecting ? "visible" : "hidden"}
                className="space-y-6"
              >
                {getFilteredTechnologies.length === 0 ? (
                  <div className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-12 text-center">
                    <div className="text-gray-400 mb-4 text-lg">
                      {translations?.noResults?.message || 'No technologies match your current filters'}
                    </div>
                    <button
                      className="px-6 py-3 bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] text-white rounded-lg font-medium hover:opacity-90 transition-opacity"
                      onClick={() => {
                        setMaturityFilters(['emerging', 'growing', 'maturing']);
                        setSelectedIndustry('lifeInsurance');
                      }}
                    >
                      {translations?.noResults?.resetButton || 'Reset Filters'}
                    </button>
                  </div>
                ) : viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {getFilteredTechnologies.map((tech: Technology) => (
                      <motion.div
                        key={tech.name}
                        variants={itemVariants}
                        className="group"
                      >
                        <div className={`bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6 hover:border-[#4FC3F7]/50 transition-all duration-300 cursor-pointer ${
                          selectedTech === tech.name ? 'ring-2 ring-[#4FC3F7] border-[#4FC3F7]' : ''
                        }`}
                        onClick={() => handleTechSelect(tech.name)}
                        >
                          {/* Card Header */}
                          <div className="flex justify-between items-start mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: maturityColors[tech.maturity as keyof typeof maturityColors] }}
                                ></div>
                                <h3 className="text-lg font-semibold text-white group-hover:text-[#4FC3F7] transition-colors">
                                  {tech.name}
                                </h3>
                              </div>
                              <p className="text-gray-300 text-sm leading-relaxed">
                                {tech.description}
                              </p>
                            </div>
                            <div className="ml-4 text-right">
                              <div className="text-2xl font-bold text-[#4FC3F7] mb-1">
                                {tech.growthRate}
                              </div>
                              <div className="text-xs text-gray-400 uppercase tracking-wide">
                                {translations?.labels?.growth || 'Growth'}
                              </div>
                            </div>
                          </div>

                          {/* Metrics */}
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <div className="bg-gray-800/50 rounded-lg p-3">
                              <div className="text-xs text-gray-400 mb-1">{translations?.labels?.relevanceScore || 'Relevance Score'}</div>
                              <div className="flex items-center gap-2">
                                <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                                  <div
                                    className="h-full bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] rounded-full"
                                    style={{ width: `${tech.relevanceScore}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium text-white">{tech.relevanceScore}%</span>
                              </div>
                            </div>
                            <div className="bg-gray-800/50 rounded-lg p-3">
                              <div className="text-xs text-gray-400 mb-1">{translations?.labels?.maturity || 'Maturity'}</div>
                              <div className="text-sm font-medium text-white capitalize">
                                {tech.maturity}
                              </div>
                            </div>
                          </div>

                          {/* Action Button */}
                          <div>
                            <button className="w-full bg-[#4FC3F7] text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-[#4FC3F7]/90 transition-colors flex items-center justify-center gap-2">
                              <Eye className="w-4 h-4" />
                              {translations?.labels?.explore || 'Explore'}
                            </button>
                          </div>

                          {/* Expanded Details */}
                          {selectedTech === tech.name && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="mt-6 pt-6 border-t border-gray-700"
                            >
                              <div className="space-y-4">
                                {/* Business Applications */}
                                {tech.businessApplications && (
                                  <div>
                                    <h4 className="text-sm font-medium text-white mb-2">{translations?.labels?.businessApplications || 'Business Applications'}</h4>
                                    <ul className="space-y-1">
                                      {tech.businessApplications?.map((app: string, i: number) => (
                                        <li key={i} className="text-xs text-gray-300 flex items-center gap-2">
                                          <div className="w-1 h-1 bg-[#4FC3F7] rounded-full"></div>
                                          {app}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}

                                {/* Key Players */}
                                {tech.keyPlayers && (
                                  <div>
                                    <h4 className="text-sm font-medium text-white mb-2">{translations?.labels?.keyPlayers || 'Key Players'}</h4>
                                    <div className="flex flex-wrap gap-2">
                                      {tech.keyPlayers?.map((player: string, i: number) => (
                                        <span key={i} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded">
                                          {player}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  // List View
                  <div className="space-y-4">
                    {getFilteredTechnologies.map((tech: Technology) => (
                      <motion.div
                        key={tech.name}
                        variants={itemVariants}
                        className="bg-gradient-to-r from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-xl border border-gray-700/50 p-4 hover:border-[#4FC3F7]/50 transition-all duration-300"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 flex-1">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: maturityColors[tech.maturity as keyof typeof maturityColors] }}
                            ></div>
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-white">{tech.name}</h3>
                              <p className="text-gray-300 text-sm">{tech.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-6">
                            <div className="text-center">
                              <div className="text-lg font-bold text-[#4FC3F7]">{tech.growthRate}</div>
                              <div className="text-xs text-gray-400">{translations?.labels?.growth || 'Growth'}</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-bold text-white">{tech.relevanceScore}%</div>
                              <div className="text-xs text-gray-400">{translations?.labels?.relevanceScore || 'Relevance'}</div>
                            </div>
                            <button className="bg-[#4FC3F7] text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-[#4FC3F7]/90 transition-colors">
                              {translations?.labels?.explore || 'Explore'}
                            </button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}

                {/* Report Generation CTA */}
                <div className="bg-gradient-to-r from-[#4FC3F7]/10 to-[#7C4DFF]/10 backdrop-blur-sm rounded-xl border border-[#4FC3F7]/30 p-8 text-center">
                  <h3 className="text-xl font-bold text-white mb-2">{translations?.reportCta?.title || 'Get Personalized Report'}</h3>
                  <p className="text-gray-300 mb-6">
                    {(translations?.reportCta?.description || 'Detailed analysis tailored to your {industry} business context').replace('{industry}', industryOptions.find(i => i.key === selectedIndustry)?.label || '')}
                  </p>
                  <button className="bg-gradient-to-r from-[#4FC3F7] to-[#7C4DFF] text-white py-3 px-8 rounded-lg font-medium hover:opacity-90 transition-opacity">
                    {translations?.reportCta?.button || 'Generate Report'}
                  </button>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PulseSection;
