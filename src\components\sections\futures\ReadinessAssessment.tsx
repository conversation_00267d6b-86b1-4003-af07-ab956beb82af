'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';

interface ReadinessAssessmentProps {
  language?: string;
  translations?: {
    title?: string;
    description?: string;
    areas?: {
      infrastructure?: string;
      dataArchitecture?: string;
      workforce?: string;
      governance?: string;
      innovation?: string;
    };
    interactivePrompt?: string;
    ui?: {
      interactiveAssessment?: string;
      guidanceText?: string;
      personalizedRecommendation?: string;
      overallReadinessScore?: string;
      valueLabels?: {
        basic?: string;
        advanced?: string;
        expert?: string;
      };
      ctaText?: string;
      ctaButton?: string;
    };
    recommendations?: {
      perfect?: string;
      high?: string;
      good?: string;
      moderate?: string;
      low?: string;
      infrastructure?: string;
      dataArchitecture?: string;
      workforce?: string;
      governance?: string;
      innovation?: string;
    };
  };
}

export default function ReadinessAssessment({ language = 'en', translations = {} }: ReadinessAssessmentProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });
  const [assessmentValues, setAssessmentValues] = useState({
    infrastructure: 3,
    dataArchitecture: 2,
    workforce: 3,
    governance: 2,
    innovation: 4
  });

  // Calculate the points for the radar chart
  const calculateRadarPoints = () => {
    const areas = ['infrastructure', 'dataArchitecture', 'workforce', 'governance', 'innovation'];
    const numPoints = areas.length;
    const angleStep = (Math.PI * 2) / numPoints;
    const radius = 120; // Base radius for the chart (increased for 300x300 viewBox)

    return areas.map((area, i) => {
      const value = assessmentValues[area as keyof typeof assessmentValues];
      const scaledRadius = (value / 5) * radius; // Scale based on value (1-5)
      const angle = i * angleStep - Math.PI / 2; // Start from top (- PI/2)
      const x = scaledRadius * Math.cos(angle) + 150; // Center at 150 for 300x300 viewBox
      const y = scaledRadius * Math.sin(angle) + 150; // Center at 150 for 300x300 viewBox
      return { x, y, area, value };
    });
  };

  const radarPoints = calculateRadarPoints();
  const polygonPoints = radarPoints.map(point => `${point.x},${point.y}`).join(' ');

  // Generate recommendations based on assessment
  const getRecommendations = () => {
    const values = Object.values(assessmentValues);
    const totalScore = values.reduce((sum, value) => sum + value, 0);
    const averageScore = totalScore / values.length;
    const minScore = Math.min(...values);

    // Handle perfect scores (all 5s)
    if (minScore === 5) {
      return translations.recommendations?.perfect || "🎉 Excellent! Your organization demonstrates exceptional readiness across all areas for next-generation technology adoption.";
    }

    // Handle high scores (average 4+ with no scores below 3)
    if (averageScore >= 4 && minScore >= 3) {
      return translations.recommendations?.high || "🚀 Outstanding! Your organization shows strong readiness for emerging technologies.";
    }

    // Handle good scores (average 3+ with no scores below 2)
    if (averageScore >= 3 && minScore >= 2) {
      return translations.recommendations?.good || "✅ Good progress! Your organization has solid foundations for technology adoption.";
    }

    // Handle moderate scores (average 2.5+ or mixed scores)
    if (averageScore >= 2.5) {
      return translations.recommendations?.moderate || "⚡ You're on the right track! Your organization shows promise for technology adoption.";
    }

    // Handle low scores
    if (averageScore < 2.5) {
      return translations.recommendations?.low || "🎯 Significant opportunities ahead! Your organization has room for improvement across multiple areas.";
    }

    // Fallback to area-specific recommendation for the weakest area
    const weakestArea = Object.entries(assessmentValues).reduce(
      (min, [area, value]) => (value < min.value ? { area, value } : min),
      { area: 'infrastructure', value: 5 }
    );

    const areaRecommendation = translations.recommendations?.[weakestArea.area as keyof typeof translations.recommendations];
    return areaRecommendation || "Focus on strengthening your foundational capabilities to improve technology readiness.";
  };

  const handleSliderChange = (area: string, value: number) => {
    setAssessmentValues(prev => ({
      ...prev,
      [area]: value
    }));
  };

  return (
    <section className="mb-8 readiness-assessment" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.h2
          className="text-3xl md:text-4xl font-bold mb-4 text-center text-white"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
        >
          {translations.title}
        </motion.h2>

        <motion.p
          className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {translations.description}
        </motion.p>

        {/* Compact Interactive Guidance */}
        <motion.div
          className="max-w-xl mx-auto text-center mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={isIntersecting ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="bg-gradient-to-r from-purple-500/10 to-cyan-500/10 border border-purple-500/30 rounded-lg p-4">
            <div className="flex items-center justify-center mb-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                <span className="text-purple-300 font-medium text-sm">Interactive Assessment</span>
                <div className="w-2 h-2 bg-cyan-500 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
              </div>
            </div>
            <p className="text-gray-300 text-sm">
              🎯 {translations.ui?.guidanceText || "Drag the sliders on the right to rate your organization's capabilities. Watch the radar chart update in real-time and receive personalized recommendations!"}
            </p>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          {/* Optimized Radar Chart */}
          <motion.div
            className="bg-deep-space border border-purple-500/20 rounded-xl p-3 sm:p-4 flex items-center justify-center order-2 lg:order-1"
            initial={{ opacity: 0, x: -40 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : { opacity: 0, x: -40 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="relative w-full max-w-[300px] aspect-square">
              <svg width="100%" height="100%" viewBox="0 0 300 300" className="overflow-visible">
                {/* Background circles */}
                {[1, 2, 3, 4, 5].map((level) => (
                  <circle
                    key={level}
                    cx="150"
                    cy="150"
                    r={level * 24}
                    fill="none"
                    stroke="#4B5563"
                    strokeWidth="0.5"
                    strokeDasharray="2,2"
                  />
                ))}

                {/* Axis lines */}
                {radarPoints.map((point, i) => (
                  <line
                    key={i}
                    x1="150"
                    y1="150"
                    x2={150 + (point.x - 150) * 5 / point.value}
                    y2={150 + (point.y - 150) * 5 / point.value}
                    stroke="#6B7280"
                    strokeWidth="0.5"
                  />
                ))}

                {/* Data polygon */}
                <polygon
                  points={polygonPoints}
                  fill="rgba(139, 92, 246, 0.2)"
                  stroke="rgba(139, 92, 246, 0.8)"
                  strokeWidth="2"
                />

                {/* Data points */}
                {radarPoints.map((point, i) => (
                  <circle
                    key={i}
                    cx={point.x}
                    cy={point.y}
                    r="4"
                    fill="#8B5CF6"
                  />
                ))}

                {/* Integrated Chart Labels */}
                {radarPoints.map((point, i) => {
                  const angle = i * (Math.PI * 2) / 5 - Math.PI / 2;
                  const labelRadius = 145; // Reduced from 180 to position closer to chart
                  const labelX = labelRadius * Math.cos(angle) + 150;
                  const labelY = labelRadius * Math.sin(angle) + 150;

                  // Get full label text
                  const labelText = translations.areas?.[point.area as keyof typeof translations.areas] || '';

                  // Split into words for multi-line display
                  const words = labelText.split(' ');
                  const line1 = words.slice(0, 2).join(' ');
                  const line2 = words.slice(2).join(' ');

                  // Calculate text anchor based on position
                  let textAnchor = "middle";
                  let dx = 0;
                  if (labelX < 130) {
                    textAnchor = "end";
                    dx = -8;
                  } else if (labelX > 170) {
                    textAnchor = "start";
                    dx = 8;
                  }

                  return (
                    <g key={i}>
                      {/* Clean background rectangle without borders */}
                      <rect
                        x={textAnchor === "end" ? labelX + dx - line1.length * 3 - 8 :
                           textAnchor === "start" ? labelX + dx - 8 :
                           labelX - line1.length * 1.5 - 8}
                        y={labelY - (line2 ? 12 : 8)}
                        width={Math.max(line1.length, line2.length) * 3 + 16}
                        height={line2 ? 24 : 16}
                        fill="rgba(0, 0, 0, 0.9)"
                        rx="4"
                        ry="4"
                      />
                      {/* Clear, readable text */}
                      <text
                        x={labelX + dx}
                        y={labelY}
                        fill="#ffffff"
                        fontSize="11"
                        fontWeight="600"
                        textAnchor={textAnchor}
                        dominantBaseline="middle"
                        className="select-none"
                      >
                        <tspan x={labelX + dx} dy={line2 ? "-0.4em" : "0"}>{line1}</tspan>
                        {line2 && (
                          <tspan x={labelX + dx} dy="1.2em">{line2}</tspan>
                        )}
                      </text>
                    </g>
                  );
                })}
              </svg>
            </div>
          </motion.div>

          {/* Compact Sliders and Recommendations */}
          <motion.div
            className="flex flex-col order-1 lg:order-2"
            initial={{ opacity: 0, x: 40 }}
            animate={isIntersecting ? { opacity: 1, x: 0 } : { opacity: 0, x: 40 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {/* Compact Interactive Prompt */}
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white text-sm">⚡</span>
                </div>
                <h3 className="text-lg font-semibold text-white">{translations.ui?.interactiveAssessment || "Interactive Assessment"}</h3>
              </div>
              <p className="text-gray-300 text-sm">{translations.interactivePrompt}</p>
            </div>

            {/* Compact Optimized Sliders */}
            <div className="space-y-2 mb-6">
              {Object.entries(translations.areas || {}).map(([key, label]) => {
                const currentValue = assessmentValues[key as keyof typeof assessmentValues];
                const percentage = ((currentValue - 1) / 4) * 100;

                return (
                  <div key={key} className="group bg-gray-800/30 rounded-lg p-2 hover:bg-gray-800/50 transition-colors duration-200">
                    {/* Ultra-Compact Label and Value Layout */}
                    <div className="flex justify-between items-center mb-1.5">
                      <label className="readiness-assessment-label text-gray-200 font-medium text-xs group-hover:text-white transition-colors duration-200 flex-1">
                        {label}
                      </label>
                      <div className="flex items-center space-x-1.5 ml-3">
                        <span className="readiness-assessment-value text-purple-400 font-bold text-sm min-w-[1.5rem] text-center">
                          {currentValue}
                        </span>
                        <span className="text-gray-500 text-xs">/5</span>
                      </div>
                    </div>

                    {/* Ultra-Compact Slider */}
                    <div className="w-full">
                      <div className="relative w-full readiness-slider-container touch-manipulation group/slider py-2">
                        {/* Custom Slider Track Background */}
                        <div className="relative w-full h-3 sm:h-2 bg-gray-700 rounded-full overflow-hidden group-hover/slider:bg-gray-600 transition-colors duration-200">
                          {/* Blue Gradient Fill */}
                          <div
                            className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full transition-all duration-300 ease-out group-hover/slider:from-purple-400 group-hover/slider:to-cyan-400"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>

                        {/* Invisible Range Input for Interaction */}
                        <input
                          type="range"
                          min="1"
                          max="5"
                          step="1"
                          value={currentValue}
                          onChange={(e) => handleSliderChange(key, parseInt(e.target.value))}
                          className="absolute top-0 left-0 w-full h-full appearance-none bg-transparent cursor-pointer opacity-0"
                          style={{
                            WebkitAppearance: 'none',
                            MozAppearance: 'none',
                            outline: 'none',
                            border: 'none',
                            boxShadow: 'none',
                            background: 'transparent'
                          }}
                          aria-label={`${label} rating`}
                        />

                        {/* Custom Slider Thumb */}
                        <div
                          className="absolute top-1/2 transform -translate-y-1/2 w-5 h-5 sm:w-4 sm:h-4 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-full border-2 border-white shadow-lg pointer-events-none transition-all duration-300 ease-out group-hover/slider:scale-110 group-hover/slider:shadow-xl"
                          style={{
                            left: `calc(${percentage}% - 10px)`,
                            boxShadow: '0 2px 8px rgba(139, 92, 246, 0.4)'
                          }}
                        />

                        {/* Minimal CSS for invisible input */}
                        <style jsx>{`
                          .readiness-slider-container input[type="range"] {
                            outline: none !important;
                            border: none !important;
                            box-shadow: none !important;
                            -webkit-appearance: none !important;
                            -moz-appearance: none !important;
                            appearance: none !important;
                            background: transparent !important;
                          }

                          .readiness-slider-container input[type="range"]:focus,
                          .readiness-slider-container input[type="range"]:focus-visible,
                          .readiness-slider-container input[type="range"]:active,
                          .readiness-slider-container input[type="range"]:hover {
                            outline: none !important;
                            border: none !important;
                            box-shadow: none !important;
                            -webkit-tap-highlight-color: transparent !important;
                          }

                          /* Hide all browser-specific slider elements */
                          .readiness-slider-container input[type="range"]::-webkit-slider-thumb {
                            appearance: none !important;
                            -webkit-appearance: none !important;
                            width: 0 !important;
                            height: 0 !important;
                            background: transparent !important;
                            border: none !important;
                            outline: none !important;
                          }

                          .readiness-slider-container input[type="range"]::-webkit-slider-runnable-track {
                            appearance: none !important;
                            -webkit-appearance: none !important;
                            background: transparent !important;
                            border: none !important;
                            outline: none !important;
                          }

                          .readiness-slider-container input[type="range"]::-moz-range-thumb {
                            appearance: none !important;
                            -moz-appearance: none !important;
                            width: 0 !important;
                            height: 0 !important;
                            background: transparent !important;
                            border: none !important;
                            outline: none !important;
                          }

                          .readiness-slider-container input[type="range"]::-moz-range-track {
                            appearance: none !important;
                            -moz-appearance: none !important;
                            background: transparent !important;
                            border: none !important;
                            outline: none !important;
                          }
                        `}</style>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Compact Recommendations */}
            <motion.div
              className="bg-gradient-to-br from-deep-space to-purple-900/20 border border-purple-500/30 rounded-xl p-4 shadow-lg"
              key={JSON.stringify(assessmentValues)} // Re-animate when values change
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white text-sm">💡</span>
                </div>
                <h3 className="text-xl font-bold text-white">{translations.ui?.personalizedRecommendation || "Personalized Recommendation"}</h3>
              </div>

              {/* Score Summary */}
              <div className="mb-4 p-3 bg-black/20 rounded-lg border border-purple-500/20">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">{translations.ui?.overallReadinessScore || "Overall Readiness Score:"}</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 h-2 bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full transition-all duration-500"
                        style={{ width: `${(Object.values(assessmentValues).reduce((sum, val) => sum + val, 0) / (5 * 5)) * 100}%` }}
                      />
                    </div>
                    <span className="text-purple-400 font-bold">
                      {Math.round((Object.values(assessmentValues).reduce((sum, val) => sum + val, 0) / 5) * 10) / 10}/5
                    </span>
                  </div>
                </div>
              </div>

              <div className="prose prose-invert max-w-none">
                <p className="text-gray-300 leading-relaxed">{getRecommendations()}</p>
              </div>

              {/* Call to Action */}
              <div className="mt-6 pt-4 border-t border-purple-500/20">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <span className="text-sm sm:text-base md:text-sm text-gray-400 text-center sm:text-left md:text-left">{translations.ui?.ctaText || "Ready to improve your readiness?"}</span>
                  <Link
                    href={`/${language}/contact`}
                    className="inline-flex items-center justify-center px-6 py-4 sm:px-6 sm:py-3 md:px-6 md:py-3 bg-gradient-to-r from-purple-600 via-purple-500 to-pink-500 hover:from-purple-700 hover:via-purple-600 hover:to-pink-600 text-white text-base sm:text-sm md:text-sm font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 min-h-[44px] touch-manipulation"
                  >
                    {translations.ui?.ctaButton || "Get Detailed Analysis"}
                  </Link>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
