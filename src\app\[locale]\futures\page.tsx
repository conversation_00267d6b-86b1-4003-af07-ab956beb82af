import { getServerTranslations } from '@/lib/i18n';
import FuturesPageContent from '@/components/sections/futures/FuturesPageContent';

export default async function FuturesPage({ params }: { params: { locale: string } | Promise<{ locale: string }> }) {
  // Handle both Promise and direct object cases
  const resolvedParams = params instanceof Promise ? await params : params

  // Get translations on the server
  const { t } = await getServerTranslations(resolvedParams.locale, ['language']);

  // Prepare translations to pass as props
  const translations = {
    title: t('futures.title'),
    subtitle: t('futures.subtitle'),
    heroTitle: t('futures.heroTitle'),
    heroSubtitle: t('futures.heroSubtitle'),
    exploreButton: t('futures.exploreButton'),
    introduction: {
      headline: t('futures.introduction.headline'),
      description: t('futures.introduction.description')
    },
    technologyHorizon: {
      title: t('futures.technologyHorizon.title'),
      nearHorizon: {
        title: t('futures.technologyHorizon.nearHorizon.title'),
        technologies: {
          edgeAI: {
            title: t('futures.technologyHorizon.nearHorizon.technologies.edgeAI.title'),
            description: t('futures.technologyHorizon.nearHorizon.technologies.edgeAI.description')
          },
          hybridQuantum: {
            title: t('futures.technologyHorizon.nearHorizon.technologies.hybridQuantum.title'),
            description: t('futures.technologyHorizon.nearHorizon.technologies.hybridQuantum.description')
          },
          zeroTrust: {
            title: t('futures.technologyHorizon.nearHorizon.technologies.zeroTrust.title'),
            description: t('futures.technologyHorizon.nearHorizon.technologies.zeroTrust.description')
          }
        }
      },
      midHorizon: {
        title: t('futures.technologyHorizon.midHorizon.title'),
        technologies: {
          neuromorphic: {
            title: t('futures.technologyHorizon.midHorizon.technologies.neuromorphic.title'),
            description: t('futures.technologyHorizon.midHorizon.technologies.neuromorphic.description')
          },
          digitalTwin: {
            title: t('futures.technologyHorizon.midHorizon.technologies.digitalTwin.title'),
            description: t('futures.technologyHorizon.midHorizon.technologies.digitalTwin.description')
          },
          ambientIntelligence: {
            title: t('futures.technologyHorizon.midHorizon.technologies.ambientIntelligence.title'),
            description: t('futures.technologyHorizon.midHorizon.technologies.ambientIntelligence.description')
          }
        }
      },
      farHorizon: {
        title: t('futures.technologyHorizon.farHorizon.title'),
        technologies: {
          autonomousEnterprise: {
            title: t('futures.technologyHorizon.farHorizon.technologies.autonomousEnterprise.title'),
            description: t('futures.technologyHorizon.farHorizon.technologies.autonomousEnterprise.description')
          },
          syntheticData: {
            title: t('futures.technologyHorizon.farHorizon.technologies.syntheticData.title'),
            description: t('futures.technologyHorizon.farHorizon.technologies.syntheticData.description')
          },
          biologicalComputing: {
            title: t('futures.technologyHorizon.farHorizon.technologies.biologicalComputing.title'),
            description: t('futures.technologyHorizon.farHorizon.technologies.biologicalComputing.description')
          }
        }
      }
    },
    digitalTwin: {
      title: t('futures.digitalTwin.title'),
      overview: {
        title: t('futures.digitalTwin.overview.title'),
        description: t('futures.digitalTwin.overview.description')
      },
      components: {
        title: t('futures.digitalTwin.components.title'),
        realityCaptureEngine: {
          title: t('futures.digitalTwin.components.realityCaptureEngine.title'),
          features: [
            t('futures.digitalTwin.components.realityCaptureEngine.features.0'),
            t('futures.digitalTwin.components.realityCaptureEngine.features.1'),
            t('futures.digitalTwin.components.realityCaptureEngine.features.2')
          ]
        },
        simulationHypervisor: {
          title: t('futures.digitalTwin.components.simulationHypervisor.title'),
          features: [
            t('futures.digitalTwin.components.simulationHypervisor.features.0'),
            t('futures.digitalTwin.components.simulationHypervisor.features.1'),
            t('futures.digitalTwin.components.simulationHypervisor.features.2')
          ]
        },
        decisionAugmentation: {
          title: t('futures.digitalTwin.components.decisionAugmentation.title'),
          features: [
            t('futures.digitalTwin.components.decisionAugmentation.features.0'),
            t('futures.digitalTwin.components.decisionAugmentation.features.1'),
            t('futures.digitalTwin.components.decisionAugmentation.features.2')
          ]
        }
      },
      implementationPathway: {
        title: t('futures.digitalTwin.implementationPathway.title'),
        steps: [
          t('futures.digitalTwin.implementationPathway.steps.0'),
          t('futures.digitalTwin.implementationPathway.steps.1'),
          t('futures.digitalTwin.implementationPathway.steps.2'),
          t('futures.digitalTwin.implementationPathway.steps.3')
        ]
      },
      caseStudy: {
        title: t('futures.digitalTwin.caseStudy.title'),
        headline: t('futures.digitalTwin.caseStudy.headline'),
        metrics: [
          t('futures.digitalTwin.caseStudy.metrics.0'),
          t('futures.digitalTwin.caseStudy.metrics.1'),
          t('futures.digitalTwin.caseStudy.metrics.2'),
          t('futures.digitalTwin.caseStudy.metrics.3')
        ]
      }
    },
    readinessAssessment: {
      title: t('futures.readinessAssessment.title'),
      description: t('futures.readinessAssessment.description'),
      areas: {
        infrastructure: t('futures.readinessAssessment.areas.infrastructure'),
        dataArchitecture: t('futures.readinessAssessment.areas.dataArchitecture'),
        workforce: t('futures.readinessAssessment.areas.workforce'),
        governance: t('futures.readinessAssessment.areas.governance'),
        innovation: t('futures.readinessAssessment.areas.innovation')
      },
      interactivePrompt: t('futures.readinessAssessment.interactivePrompt'),
      ui: {
        interactiveAssessment: t('futures.readinessAssessment.ui.interactiveAssessment'),
        guidanceText: t('futures.readinessAssessment.ui.guidanceText'),
        personalizedRecommendation: t('futures.readinessAssessment.ui.personalizedRecommendation'),
        overallReadinessScore: t('futures.readinessAssessment.ui.overallReadinessScore'),
        valueLabels: {
          basic: t('futures.readinessAssessment.ui.valueLabels.basic'),
          advanced: t('futures.readinessAssessment.ui.valueLabels.advanced'),
          expert: t('futures.readinessAssessment.ui.valueLabels.expert')
        },
        ctaText: t('futures.readinessAssessment.ui.ctaText'),
        ctaButton: t('futures.readinessAssessment.ui.ctaButton')
      },
      recommendations: {
        perfect: t('futures.readinessAssessment.recommendations.perfect'),
        high: t('futures.readinessAssessment.recommendations.high'),
        good: t('futures.readinessAssessment.recommendations.good'),
        moderate: t('futures.readinessAssessment.recommendations.moderate'),
        low: t('futures.readinessAssessment.recommendations.low'),
        infrastructure: t('futures.readinessAssessment.recommendations.infrastructure'),
        dataArchitecture: t('futures.readinessAssessment.recommendations.dataArchitecture'),
        workforce: t('futures.readinessAssessment.recommendations.workforce'),
        governance: t('futures.readinessAssessment.recommendations.governance'),
        innovation: t('futures.readinessAssessment.recommendations.innovation')
      }
    }
  };

  return <FuturesPageContent locale={resolvedParams.locale} translations={translations} />;
}
